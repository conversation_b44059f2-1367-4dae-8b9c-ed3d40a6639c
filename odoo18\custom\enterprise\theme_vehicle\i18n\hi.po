# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_vehicle
# 
# Translators:
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:31+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quotes_carousel_minimal
msgid ""
"\" A premier choice for luxury cars. <br/>Expert maintenance, exquisite "
"service, and utmost professionalism. \""
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quotes_carousel_minimal
msgid ""
"\" Outstanding service and craftsmanship! <br/>They ensure every luxury "
"vehicle is in perfect condition. \""
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quotes_carousel_minimal
msgid ""
"\" Their service is exceptional. <br/>Premium care, top-notch quality, and "
"unparalleled attention to detail. \""
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid "$ 2.4B"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$1,200.00"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$2,500.00"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$3,000.00"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$4,000.00"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$450.00"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$800.00"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid "+300,000"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid ""
"75% of our clients choose to upgrade to the latest models every 3 years, "
"showcasing strong brand loyalty and trust."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_call_to_action
msgid "<b>50,000 pre-orders</b> already registered."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_text
msgid "<b>A greener lifestyle</b>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid "<b>The world is yours</b>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid ""
"<br/>Offering a wide selection of quality cars to suit every need and "
"budget.<br/><br/>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(29, 32, 48) 0%, rgb(222, 222, 222) 49%);\">\n"
"            87%\n"
"        </font>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Top-"
"rated vehicles"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.configurator_s_three_columns
msgid "<span class=\"d-inline-block mb-4\"><b>From $14,000</b></span>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.configurator_s_three_columns
msgid "<span class=\"d-inline-block mb-4\"><b>From $19,000</b></span>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.configurator_s_three_columns
msgid "<span class=\"d-inline-block mb-4\"><b>From $25,000</b></span>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_picture
msgid ""
"<span class=\"text-o-color-3\">Our uniquely designed LED headlights are not "
"only gorgeous but powerfully light your way.</span>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_title
msgid "A Deep Dive into Luxury and Innovation"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid ""
"A bold statement in every detail, the Koran X captures the spirit of modern "
"adventure. Built for those who refuse to compromise on performance and "
"style."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid ""
"Achieve optimal performance with a sleek, aerodynamic design that reduces "
"drag and enhances stability, ensuring superior speed and efficiency."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "Aerodynamic Design"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_title
msgid "All our models"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid ""
"Benefit from competitive financing plans and leasing options designed to "
"make your car purchase as affordable as possible."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Bespoke Automotive Solutions"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_freegrid
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quadrant
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_striped_center_top
msgid "Book a Service"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid "Book a test drive"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #1"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #2"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #3"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #4"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #5"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #6"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_picture
msgid ""
"Bring the KORAN to life on your smartphone or tablet so you can visualise it"
" for yourself"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid "Browse our inventory   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid ""
"Clients saved $2.4 billion by choosing our eco-efficient and cost-effective "
"vehicles."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Complete maintenance service covering engine check, transmission fluid "
"change, brake inspection, and system diagnostics for optimal performance."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid "Comprehensive Support"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Comprehensive detailing service including exterior polishing, interior "
"cleaning, and protective coatings for a showroom finish."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.configurator_s_three_columns
msgid "Configure"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cta_box
msgid "Contact us"
msgstr "हमसे संपर्क करें"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Custom Interior Upgrades"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid "Custom Vehicle Options"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid ""
"Designed for the thrill-seeker, the Koran GT brings race-inspired "
"performance to the streets. Precision-tuned for speed, it promises an "
"driving experience."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Discover Excellence in Luxury Vehicle Care"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Discover our exclusive range of services for luxury vehicles, designed to "
"provide unparalleled quality and sophistication. Experience excellence on "
"every drive."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid ""
"Discover unique and advanced features that set our vehicles apart, providing"
" you with exceptional performance and cutting-edge technology."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid ""
"Discover unparalleled automotive care with our expert services that blend "
"precision, luxury, and exceptional performance."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_framed_intro
msgid "Drive Your Dream: Quality Cars for Every Journey"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_striped_center_top
msgid "Drive with Confidence and Style"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_card_offset
msgid "Drive with confidence."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_unveil
msgid "Drive your Passion"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid "Driving excellence with luxury vehicles"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features
msgid "Dual Motor"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_text
msgid "Electric Driving"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_masonry_block_image_texts_image_template
msgid "Electrifying <b>Performance</b>."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid ""
"Elevate your driving experience with our bespoke maintenance and repair "
"solutions."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid ""
"Enjoy exceptional control and responsiveness with advanced suspension "
"systems and precise steering, offering unmatched agility and cornering "
"capabilities."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Excellence and Craftsmanship"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Exclusive Exterior Customization"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid "Exclusive Features"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid ""
"Experience thrilling acceleration and top speeds with our sports car’s high-"
"performance engine, designed for an exhilarating driving experience on both "
"the track and the road."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid "Experience top-tier luxury"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid "Expert Vehicle Service"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Expert technicians specializing in luxury and exotic vehicles"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid ""
"Explore more and find premium services for your luxury vehicle, crafted to "
"ensure unrivaled quality and customer satisfaction."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_unveil
msgid ""
"Explore our exceptional range of cars designed for performance and luxury."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid ""
"Explore our extensive inventory of new and pre-owned cars to find the "
"perfect vehicle that fits your needs and budget."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_images_mosaic
msgid "Explore premium maintenance and bespoke care for your high-end car."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid "Find your perfect ride<br/>with premium car sales"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid "Flexible Financing Options"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Full-Service Maintenance"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "High-Performance Engine"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "High-Performance Tuning"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid ""
"Immerse yourself in a driver-focused cockpit with sport seats, intuitive "
"controls, and premium materials, all crafted to enhance your driving "
"pleasure and comfort."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Innovation and Performance"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_call_to_action
msgid "Join the early joiners or request for a trial on track."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid ""
"Keep your car in top condition with our comprehensive service and "
"maintenance options, handled by skilled technicians."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_card_offset
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_freegrid
msgid ""
"Keep your vehicle in top condition with our comprehensive repair and "
"maintenance services. From cars to motorbikes, we offer expert solutions to "
"ensure safe and smooth rides."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quadrant
msgid ""
"Keep your vehicle in top condition with our expert repair and maintenance "
"services. From cars to motorbikes, we’ve got you covered.<br/><br/> Drive "
"with confidence and style."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_striped_center_top
msgid ""
"Keep your vehicles running smoothly with our expert repair and maintenance "
"services, designed for performance and reliability."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid "Key Metrics of our Achievements"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Koran GT"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Koran Mini"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Koran X"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features
msgid "Long Range"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cta_box
msgid ""
"Looking for a leasing plan or a brand new car ? We got you covered<br/><br/>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Luxury Vehicle Servicing"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_media_list
msgid "News"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid "No compromise"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_images_mosaic
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Our Luxury Vehicle Services"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_references
msgid "Our Partners"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid "Our cars"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid ""
"Our commitment to innovation, performance, and sustainability drives us "
"forward, enabling us to deliver excellence across all categories of the "
"automotive industry."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid ""
"Our team provides full support throughout your vehicle purchase and "
"ownership experience, from initial consultation to ongoing service."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_grid
msgid "Our web store"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid ""
"Performance and design were the key words during the conception of this "
"super car. No compromises were made to keep the pleasure of driving despite "
"those constraints."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Performance tuning service including engine optimization, suspension "
"upgrades, and exhaust modifications to enhance driving dynamics and power."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Personalized exterior modifications such as custom paint jobs, aerodynamic "
"enhancements, and bespoke alloy wheels to reflect your style."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Personalized service to ensure your vehicle's optimal performance"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "Precision Handling"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_striped_center_top
msgid "Precision in every detail"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Premium Detailing Package"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Premium Services for Your High-End Automotive Needs"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid "Premium automotive services"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_card_offset
msgid "Reliable Vehicle Services &amp; Repairs"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_freegrid
msgid "Reliable Vehicle Services and Maintenance"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_masonry_block_image_texts_image_template
msgid "Smarter <b>Range</b>."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Specialized servicing tailored for high-end vehicles, including advanced "
"diagnostics, custom parts, and manufacturer-recommended procedures."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "Sporty Interior"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cover
msgid "Start the engine"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "State-of-the-art facilities for superior maintenance"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Sustainable Luxury"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Tailored interior enhancements including premium leather upholstery, bespoke"
" trims, and advanced infotainment systems for a unique driving experience."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid "Take advantage of a free track trial to discover it."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cover
msgid "Take it all. Compliments too. <br/><br/>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cover
msgid "The New KORAN X"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cta_box
msgid "The best car dealer<br/>in your local area"
msgstr ""

#. module: theme_vehicle
#: model:ir.model,name:theme_vehicle.model_theme_utils
msgid "Theme Utils"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features
msgid "Top Speed"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_title
msgid ""
"Transform your driving experience with our exclusive collection, where "
"opulence meets cutting-edge technology. Elevate your journey with vehicles "
"that blend sophistication and performance seamlessly."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Trust us to keep your luxury vehicle in pristine condition"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "Unleash the Power of Our Sports Cars"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid ""
"Unleashing power with elegance, the Koran Mini blends advanced engineering "
"with classic design, delivering a thrilling ride that’s both swift and "
"smooth."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quadrant
msgid "Vehicle Services"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_picture
msgid "View the all-new KORAN in 3D wherever you are"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid ""
"We offer personalized vehicle configurations to match your preferences and "
"needs, ensuring you get the perfect fit for your lifestyle."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid ""
"We offer the latest advancements in automotive technology and design. "
"Leveraging cutting-edge features, we ensure every vehicle provides "
"exceptional performance and sophistication."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid "We proudly serve over 300,000 satisfied customers."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid ""
"We provide tailored luxury vehicles designed to meet your specific "
"preferences."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "What we offer to our clients"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid "Wide Vehicle Selection"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid ""
"With extensive experience and superior craftsmanship, we provide high-end "
"vehicles that deliver unmatched performance and style, ensuring a luxurious "
"driving experience."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_grid
msgid "Your advantages"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Your satisfaction and environmental impact are our priorities."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_big_number
msgid "customer satisfaction"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "✽  Customization &amp; Upgrades"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "✽  Maintenance &amp; Care"
msgstr ""
