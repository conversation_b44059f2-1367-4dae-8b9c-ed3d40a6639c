<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="transifex_code_translation_tree_view" model="ir.ui.view">
        <field name="name">transifex.code.translation.list</field>
        <field name="model">transifex.code.translation</field>
        <field name="arch" type="xml">
            <list string="Transifex Code Translation" js_class="transifex_code_translation_tree">
                <field name="source"/>
                <field name="value"/>
                <field name="module"/>
                <field name="lang"/>
                <field name="transifex_url"
                       widget="url"
                       text="Contribute"
                       string="Transifex" />
            </list>
        </field>
    </record>

    <record id="transifex_code_translation_view_search" model="ir.ui.view">
        <field name="name">transifex.code.translation.view.search</field>
        <field name="model">transifex.code.translation</field>
        <field name="arch" type="xml">
            <search string="Search Code Translations">
                <field name="module"/>
                <field name="lang"/>
                <field name="source"/>
                <field name="value"/>
                <separator/>
                <filter string="Not Translated" name="not_translated" domain="[('value', '=', '')]"/>
                <group string="Group By">
                    <filter string="Module" name="group_by_module" context="{'group_by': 'module'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_code_translations" model="ir.actions.server">
        <field name="name">Transifex Code Translations</field>
        <field name="model_id" ref="transifex.model_transifex_code_translation"/>
        <field name="groups_id" eval="[(4, ref('base.group_system'))]"/>
        <field name="state">code</field>
        <field name="code">action = model._open_code_translations()</field>
    </record>
    <menuitem action="action_code_translations" id="menu_transifex_code_translations" parent="base.menu_translation_app"/>

</odoo>
