# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_knowledge
# 
# Translators:
# <PERSON> <and<PERSON>.<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:16+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "<i class=\"fa fa-lg fa-bars\" title=\"Toggle aside menu\"/>"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Article not Published"
msgstr "Artikel inte publicerad"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "Article not found"
msgstr "Artikel inte funnen"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Article shared to web"
msgstr "Artikel delad på webben"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__can_publish
msgid "Can Publish"
msgstr "Kan publicera"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__is_published
#: model_terms:ir.ui.view,arch_db:website_knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:website_knowledge.knowledge_article_view_tree
msgid "Is Published"
msgstr "Är publicerad"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "It and its published children can be read by anyone"
msgstr "Den, och dess publicerade underartiklar, kan läsas av alla"

#. module: website_knowledge
#: model:ir.model,name:website_knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr "Kunskapsartikel"

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.articles_template
msgid "Load more"
msgstr "Ladda mer"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/editor/embedded_components/view/view_placeholder.xml:0
msgid "Log in"
msgstr "Logga in"

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.public_sidebar
msgid "No article found"
msgstr "Inga artiklar funna"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Only specific people can access"
msgstr "Endast specifika användare har åtkomst"

#. module: website_knowledge
#: model:ir.actions.server,name:website_knowledge.knowledge_action_publish_articles
msgid "Publish Articles"
msgstr "Publicera artiklar"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Publish this Article and its children on the web"
msgstr "Publicera artikel, och dess underartiklar, på webben"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Search an article..."
msgstr "Sök en artikel..."

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Share to web"
msgstr "Dela till webben"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "Sign in"
msgstr "Logga in"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__summary
msgid "Summary"
msgstr "Sammanfattning"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid ""
"The article you are trying the read has either been removed or you do not "
"have access to it."
msgstr ""
"Artikeln du försöker nå har antingen raderats eller så saknar du åtkomst "
"till den."

#. module: website_knowledge
#: model:ir.model.fields,help:website_knowledge.field_knowledge_article__website_url
msgid "The full URL to access the document through the website."
msgstr "Fullständig URL för åtkomst av dokument via webbplatsen."

#. module: website_knowledge
#. odoo-python
#: code:addons/website_knowledge/controllers/main.py:0
msgid ""
"This Article cannot be unfolded. Either you lost access to it or it has been"
" deleted."
msgstr ""
"Denna artikel kan inte fällas ut. Antingen har den raderats eller så saknar "
"du åtkomst till den."

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/editor/embedded_components/view/view_placeholder.xml:0
msgid "This view is only available for internal users"
msgstr "Denna vy är bara tillgänglig för inloggade användare"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Toggle aside menu"
msgstr "Växla sidomeny"

#. module: website_knowledge
#: model:ir.actions.server,name:website_knowledge.knowledge_action_unpublish_articles
msgid "Unpublish Articles"
msgstr "Avpublicera artikel"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "Untitled"
msgstr "Namnlös"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__website_published
msgid "Visible on current website"
msgstr "Synlig på nuvarande webbplats"

#. module: website_knowledge
#: model:ir.model,name:website_knowledge.model_website
msgid "Website"
msgstr "Webbplats"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__website_url
msgid "Website URL"
msgstr "Webbplatsens URL"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "loader"
msgstr "laddare"
