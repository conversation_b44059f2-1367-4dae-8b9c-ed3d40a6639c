<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_hr_recruitment_tree_url" model="ir.ui.view" >
        <field name="name">hr.recruitment.list.inherit.url</field>
        <field name="model">hr.recruitment.source</field>
        <field name="inherit_id" ref="hr_recruitment.hr_recruitment_source_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='email']" position="before">
                <field name="url" widget="CopyClipboardURL"/>
            </xpath>
        </field>
    </record>

    <record id="view_hr_job_form_website_published_button" model="ir.ui.view" >
        <field name="name">hr.job.form.inherit.published.button</field>
        <field name="model">hr.job</field>
        <field name="inherit_id" ref="hr_recruitment.hr_job_survey"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <field name="is_published" widget="website_redirect_button"/>
            </div>
            <xpath expr="//div[hasclass('oe_title')]" position="before">
                <div class="float-end">
                    <field name="website_published" widget="boolean_toggle_labeled" nolabel="1" options="{'false_label': 'Not Published', 'true_label': 'Published'}"/>
                </div>
            </xpath>
            <xpath expr="//div[@name='recruitment_target']" position="after">
                <field name="website_id" options="{'no_create': True}" domain="['|', ('company_id', '=', company_id), ('company_id', '=', False)]" groups="website.group_multi_website"/>
            </xpath>
        </field>
    </record>

    <record id="view_hr_job_form_inherit_website" model="ir.ui.view">
        <field name="name">hr.job.form</field>
        <field name="model">hr.job</field>
        <field name="inherit_id" ref="hr.view_hr_job_form"/>
        <field name="arch" type="xml">
            <field name="description" position="attributes">
                <attribute name="placeholder">e.g. Summarize the position in one or two lines that will be displayed on the Jobs list page...</attribute>
            </field>
        </field>
    </record>

    <record id="view_hr_job_tree_inherit_website" model="ir.ui.view">
        <field name="name">hr.job.list</field>
        <field name="model">hr.job</field>
        <field name="inherit_id" ref="hr_recruitment.hr_job_view_tree_inherit"/>
        <field name="arch" type="xml">
            <field name="no_of_employee" position="before">
                <field name="website_id" groups="website.group_multi_website" optional="hide"/>
            </field>
            <xpath expr="//field[@name='alias_id']" position="before">
                <field name="is_published" string="Published"/>
            </xpath>
        </field>
    </record>
</odoo>
