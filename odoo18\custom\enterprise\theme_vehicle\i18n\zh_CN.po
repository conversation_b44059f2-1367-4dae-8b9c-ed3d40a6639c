# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_vehicle
# 
# Translators:
# Wil Odoo, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:31+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quotes_carousel_minimal
msgid ""
"\" A premier choice for luxury cars. <br/>Expert maintenance, exquisite "
"service, and utmost professionalism. \""
msgstr "\"豪华汽车的首选。<br/>专业保养、精致服务、极致敬业。”"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quotes_carousel_minimal
msgid ""
"\" Outstanding service and craftsmanship! <br/>They ensure every luxury "
"vehicle is in perfect condition. \""
msgstr "“出色的服务和工艺！<br/>他们确保每辆豪华车都处于完美状态。”"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quotes_carousel_minimal
msgid ""
"\" Their service is exceptional. <br/>Premium care, top-notch quality, and "
"unparalleled attention to detail. \""
msgstr "“他们的服务非常出色。<br/>优质的护理、一流的质量以及对细节无与伦比的关注。”"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid "$ 2.4B"
msgstr "$ 2.4B"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$1,200.00"
msgstr "$1,200.00"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$2,500.00"
msgstr "$2,500.00"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$3,000.00"
msgstr "$3,000.00"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$4,000.00"
msgstr "$4,000.00"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$450.00"
msgstr "$450.00"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$800.00"
msgstr "$800.00"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid "+300,000"
msgstr "+300,000"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid ""
"75% of our clients choose to upgrade to the latest models every 3 years, "
"showcasing strong brand loyalty and trust."
msgstr "我们 75% 的客户选择每 3 年升级到最新型号，展现出强大的品牌忠诚度和信任度。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_call_to_action
msgid "<b>50,000 pre-orders</b> already registered."
msgstr "已有<b>50,000 份预订单</b>登记。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_text
msgid "<b>A greener lifestyle</b>"
msgstr "<b>更环保的生活方式</b>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid "<b>The world is yours</b>"
msgstr "<b>世界属于你</b>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid ""
"<br/>Offering a wide selection of quality cars to suit every need and "
"budget.<br/><br/>"
msgstr "<br/>提供多种优质汽车选择，以满足各种需求和预算。<br/><br/>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(29, 32, 48) 0%, rgb(222, 222, 222) 49%);\">\n"
"            87%\n"
"        </font>"
msgstr ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(29, 32, 48) 0%, rgb(222, 222, 222) 49%);\">\n"
"            87%\n"
"        </font>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Top-"
"rated vehicles"
msgstr "<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  最受好评的车辆"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.configurator_s_three_columns
msgid "<span class=\"d-inline-block mb-4\"><b>From $14,000</b></span>"
msgstr "<span class=\"d-inline-block mb-4\"><b>14,000 美元起</b></span>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.configurator_s_three_columns
msgid "<span class=\"d-inline-block mb-4\"><b>From $19,000</b></span>"
msgstr "<span class=\"d-inline-block mb-4\"><b>19,000 美元起</b></span>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.configurator_s_three_columns
msgid "<span class=\"d-inline-block mb-4\"><b>From $25,000</b></span>"
msgstr "<span class=\"d-inline-block mb-4\"><b>25,000 美元起</b></span>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_picture
msgid ""
"<span class=\"text-o-color-3\">Our uniquely designed LED headlights are not "
"only gorgeous but powerfully light your way.</span>"
msgstr ""
"<span class=\"text-o-color-3\">我们设计独特的 LED 前大灯不仅绚丽多彩，而且还能为您提供强有力的照明。</span>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_title
msgid "A Deep Dive into Luxury and Innovation"
msgstr "深入了解奢华与创新"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid ""
"A bold statement in every detail, the Koran X captures the spirit of modern "
"adventure. Built for those who refuse to compromise on performance and "
"style."
msgstr "Koran X 的每一个细节都是大胆的宣言，它捕捉到了现代探险的精神。专为那些拒绝在性能和风格上妥协的人打造。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid ""
"Achieve optimal performance with a sleek, aerodynamic design that reduces "
"drag and enhances stability, ensuring superior speed and efficiency."
msgstr "流线型的空气动力学设计可减少阻力，增强稳定性，确保卓越的速度和效率，从而实现最佳性能。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "Aerodynamic Design"
msgstr "空气动力设计"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_title
msgid "All our models"
msgstr "我们的所有模型"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid ""
"Benefit from competitive financing plans and leasing options designed to "
"make your car purchase as affordable as possible."
msgstr "受益于有竞争力的融资计划和租赁选项，旨在使您的汽车购买尽可能实惠。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Bespoke Automotive Solutions"
msgstr "定制汽车解决方案"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_freegrid
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quadrant
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_striped_center_top
msgid "Book a Service"
msgstr "预订服务"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid "Book a test drive"
msgstr "预约试驾"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #1"
msgstr "制动器 #1"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #2"
msgstr "制动器 #2"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #3"
msgstr "制动器 #3"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #4"
msgstr "制动器 #4"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #5"
msgstr "制动器 #5"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #6"
msgstr "制动器 #6"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_picture
msgid ""
"Bring the KORAN to life on your smartphone or tablet so you can visualise it"
" for yourself"
msgstr "在智能手机或平板电脑上将《古兰经》活灵活现地呈现在您的眼前，让您亲眼一睹《古兰经》的内容"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid "Browse our inventory   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr "浏览我们的库存   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid ""
"Clients saved $2.4 billion by choosing our eco-efficient and cost-effective "
"vehicles."
msgstr "客户选择我们的环保高效车辆，节省了 24 亿美元。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Complete maintenance service covering engine check, transmission fluid "
"change, brake inspection, and system diagnostics for optimal performance."
msgstr "全面的保养服务包括发动机检查、更换变速箱油、制动检查和系统诊断，以实现最佳性能。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid "Comprehensive Support"
msgstr "综合支持"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Comprehensive detailing service including exterior polishing, interior "
"cleaning, and protective coatings for a showroom finish."
msgstr "全面的细节服务，包括外部抛光、内部清洁和展厅装饰的保护涂层。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.configurator_s_three_columns
msgid "Configure"
msgstr "配置"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cta_box
msgid "Contact us"
msgstr "联系我们"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Custom Interior Upgrades"
msgstr "定制内饰升级"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid "Custom Vehicle Options"
msgstr "定制车辆选项"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid ""
"Designed for the thrill-seeker, the Koran GT brings race-inspired "
"performance to the streets. Precision-tuned for speed, it promises an "
"driving experience."
msgstr "Koran GT 专为追求刺激的人士而设计，将赛车的性能带到了街头。它经过精密调校，速度极快，为您带来无与伦比的驾驶体验。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Discover Excellence in Luxury Vehicle Care"
msgstr "探索卓越的豪华车辆护理"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Discover our exclusive range of services for luxury vehicles, designed to "
"provide unparalleled quality and sophistication. Experience excellence on "
"every drive."
msgstr "了解我们为豪华车提供的一系列专属服务，这些服务旨在提供无与伦比的品质和精致。体验每次驾驶的卓越感受。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid ""
"Discover unique and advanced features that set our vehicles apart, providing"
" you with exceptional performance and cutting-edge technology."
msgstr "发现我们车辆与众不同的先进功能，为您提供卓越性能和尖端技术。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid ""
"Discover unparalleled automotive care with our expert services that blend "
"precision, luxury, and exceptional performance."
msgstr "我们的专业服务融合了精准、豪华和卓越性能，让您体验无与伦比的汽车护理服务。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_framed_intro
msgid "Drive Your Dream: Quality Cars for Every Journey"
msgstr "驾驭梦想：适合每段旅程的优质汽车"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_striped_center_top
msgid "Drive with Confidence and Style"
msgstr "自信而时尚地驾驶"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_card_offset
msgid "Drive with confidence."
msgstr "自信驾驶。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_unveil
msgid "Drive your Passion"
msgstr "激发激情"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid "Driving excellence with luxury vehicles"
msgstr "驾驶豪华汽车，追求卓越"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features
msgid "Dual Motor"
msgstr "双马达"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_text
msgid "Electric Driving"
msgstr "电动驾驶"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_masonry_block_image_texts_image_template
msgid "Electrifying <b>Performance</b>."
msgstr "震撼人心的<b>表演</b>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid ""
"Elevate your driving experience with our bespoke maintenance and repair "
"solutions."
msgstr "我们为您量身定制保养和维修解决方案，提升您的驾驶体验。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid ""
"Enjoy exceptional control and responsiveness with advanced suspension "
"systems and precise steering, offering unmatched agility and cornering "
"capabilities."
msgstr "先进的悬挂系统和精确的转向系统提供了无与伦比的灵活性和转弯能力，让您尽享非凡的操控性和响应性。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Excellence and Craftsmanship"
msgstr "卓越与工艺"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Exclusive Exterior Customization"
msgstr "独家外观定制"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid "Exclusive Features"
msgstr "独家特色"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid ""
"Experience thrilling acceleration and top speeds with our sports car’s high-"
"performance engine, designed for an exhilarating driving experience on both "
"the track and the road."
msgstr "我们的跑车配备高性能发动机，无论是在赛道上还是在公路上，都能带来令人兴奋的驾驶体验。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid "Experience top-tier luxury"
msgstr "体验顶级奢华"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid "Expert Vehicle Service"
msgstr "专业车辆服务"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Expert technicians specializing in luxury and exotic vehicles"
msgstr "专业技师，精通豪华和奇特车辆"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid ""
"Explore more and find premium services for your luxury vehicle, crafted to "
"ensure unrivaled quality and customer satisfaction."
msgstr "了解更多信息，为您的豪华车寻找优质服务，确保无与伦比的质量和客户满意度。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_unveil
msgid ""
"Explore our exceptional range of cars designed for performance and luxury."
msgstr "探索我们为性能和豪华而设计的卓越汽车系列。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid ""
"Explore our extensive inventory of new and pre-owned cars to find the "
"perfect vehicle that fits your needs and budget."
msgstr "探索我们丰富的新车和二手车库存，找到适合您需求和预算的完美车型。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_images_mosaic
msgid "Explore premium maintenance and bespoke care for your high-end car."
msgstr "探索高端汽车的高级保养和定制护理。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid "Find your perfect ride<br/>with premium car sales"
msgstr "寻找您的完美座驾，<br/>尽在我们的高端汽车销售。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid "Flexible Financing Options"
msgstr "灵活的融资选项"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Full-Service Maintenance"
msgstr "全方位维护服务"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "High-Performance Engine"
msgstr "高性能发动机"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "High-Performance Tuning"
msgstr "高性能调校"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid ""
"Immerse yourself in a driver-focused cockpit with sport seats, intuitive "
"controls, and premium materials, all crafted to enhance your driving "
"pleasure and comfort."
msgstr "沉浸在以驾驶员为中心的驾驶舱中，配备运动座椅、直观的控制装置和优质材料，所有这些都旨在增强您的驾驶乐趣和舒适度。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Innovation and Performance"
msgstr "创新与性能"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_call_to_action
msgid "Join the early joiners or request for a trial on track."
msgstr "加入早鸟行列，或申请跟踪试用."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid ""
"Keep your car in top condition with our comprehensive service and "
"maintenance options, handled by skilled technicians."
msgstr "由技术娴熟的技师为您提供全面的服务和保养方案，让您的爱车保持最佳状态。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_card_offset
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_freegrid
msgid ""
"Keep your vehicle in top condition with our comprehensive repair and "
"maintenance services. From cars to motorbikes, we offer expert solutions to "
"ensure safe and smooth rides."
msgstr "我们提供全面的维修和保养服务，让您的爱车保持最佳状态。从汽车到摩托车，我们都能提供专业的解决方案，确保安全平稳地行驶。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quadrant
msgid ""
"Keep your vehicle in top condition with our expert repair and maintenance "
"services. From cars to motorbikes, we’ve got you covered.<br/><br/> Drive "
"with confidence and style."
msgstr "我们的专业维修和保养服务让您的爱车保持最佳状态。从汽车到摩托车，我们都能为您提供服务。<br/><br/>在驾驶的同时，尽显自信和风度。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_striped_center_top
msgid ""
"Keep your vehicles running smoothly with our expert repair and maintenance "
"services, designed for performance and reliability."
msgstr "我们提供专业的维修和保养服务，确保您的车辆平稳运行，保证性能和可靠性。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid "Key Metrics of our Achievements"
msgstr "我们取得成就的关键指标"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Koran GT"
msgstr "古兰经 GT"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Koran Mini"
msgstr "迷你古兰经"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Koran X"
msgstr "古兰经 X"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features
msgid "Long Range"
msgstr "远距离"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cta_box
msgid ""
"Looking for a leasing plan or a brand new car ? We got you covered<br/><br/>"
msgstr "正在寻找租赁计划或全新汽车？我们为您提供服务<br/><br/>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Luxury Vehicle Servicing"
msgstr "豪华车维护"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_media_list
msgid "News"
msgstr "新闻"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid "No compromise"
msgstr "不妥协"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_images_mosaic
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Our Luxury Vehicle Services"
msgstr "我们的豪华车服务"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_references
msgid "Our Partners"
msgstr "我们的合作伙伴"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid "Our cars"
msgstr "我们的汽车"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid ""
"Our commitment to innovation, performance, and sustainability drives us "
"forward, enabling us to deliver excellence across all categories of the "
"automotive industry."
msgstr "我们对创新、性能和可持续发展的承诺推动我们不断前进，使我们能够在汽车行业的所有类别中提供卓越的服务。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid ""
"Our team provides full support throughout your vehicle purchase and "
"ownership experience, from initial consultation to ongoing service."
msgstr "从最初咨询到持续服务，我们的团队将在您购买和驾驶车辆的整个过程中为您提供全方位的支持。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_grid
msgid "Our web store"
msgstr "我们的网店"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid ""
"Performance and design were the key words during the conception of this "
"super car. No compromises were made to keep the pleasure of driving despite "
"those constraints."
msgstr "性能和设计是这款超级跑车设计过程中的关键词。尽管有这些限制，但为了保持驾驶乐趣，我们没有做出任何妥协。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Performance tuning service including engine optimization, suspension "
"upgrades, and exhaust modifications to enhance driving dynamics and power."
msgstr "性能改装服务包括发动机优化、悬挂系统升级和排气系统改装，以增强驾驶动力和功率。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Personalized exterior modifications such as custom paint jobs, aerodynamic "
"enhancements, and bespoke alloy wheels to reflect your style."
msgstr "个性化的外观改装，如定制喷漆、空气动力增强装置和定制合金车轮，以体现您的风格。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Personalized service to ensure your vehicle's optimal performance"
msgstr "个性化服务确保您的车辆发挥最佳性能"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "Precision Handling"
msgstr "精密处理"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_striped_center_top
msgid "Precision in every detail"
msgstr "精确到每一个细节"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Premium Detailing Package"
msgstr "高级汽车美容套餐"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Premium Services for Your High-End Automotive Needs"
msgstr "满足您高端汽车需求的优质服务"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid "Premium automotive services"
msgstr "高端汽车服务"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_card_offset
msgid "Reliable Vehicle Services &amp; Repairs"
msgstr "可靠的车辆服务 &amp; 维修"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_freegrid
msgid "Reliable Vehicle Services and Maintenance"
msgstr "可靠的车辆服务 &amp; 维护"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_masonry_block_image_texts_image_template
msgid "Smarter <b>Range</b>."
msgstr "更智能的<b>范围</b>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Specialized servicing tailored for high-end vehicles, including advanced "
"diagnostics, custom parts, and manufacturer-recommended procedures."
msgstr "为高端车辆量身定制的专业服务，包括高级诊断、定制零件和制造商推荐的程序。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "Sporty Interior"
msgstr "动感内饰"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cover
msgid "Start the engine"
msgstr "启动发动机"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "State-of-the-art facilities for superior maintenance"
msgstr "先进的设施，卓越的维护"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Sustainable Luxury"
msgstr "可持续的奢华"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Tailored interior enhancements including premium leather upholstery, bespoke"
" trims, and advanced infotainment systems for a unique driving experience."
msgstr "量身定制的内饰包括高级真皮内饰、定制装饰和先进的信息娱乐系统，带来独一无二的驾驶体验。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid "Take advantage of a free track trial to discover it."
msgstr "利用免费试听来了解它。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cover
msgid "Take it all. Compliments too. <br/><br/>"
msgstr "尽在掌握，赞誉随行。<br/><br/>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cover
msgid "The New KORAN X"
msgstr "新 KORAN X"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cta_box
msgid "The best car dealer<br/>in your local area"
msgstr "当地最好的汽车经销商<br/>"

#. module: theme_vehicle
#: model:ir.model,name:theme_vehicle.model_theme_utils
msgid "Theme Utils"
msgstr "主题用途"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features
msgid "Top Speed"
msgstr "最高速度"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_title
msgid ""
"Transform your driving experience with our exclusive collection, where "
"opulence meets cutting-edge technology. Elevate your journey with vehicles "
"that blend sophistication and performance seamlessly."
msgstr "我们的独家系列将奢华与尖端技术完美融合，为您带来全新的驾驶体验。精致与性能完美融合，让您的旅程更上一层楼。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Trust us to keep your luxury vehicle in pristine condition"
msgstr "相信我们能让您的豪华车保持良好状态"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "Unleash the Power of Our Sports Cars"
msgstr "释放我们跑车的力量"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid ""
"Unleashing power with elegance, the Koran Mini blends advanced engineering "
"with classic design, delivering a thrilling ride that’s both swift and "
"smooth."
msgstr "优雅中释放强劲动力，Koran Mini 将先进工程与经典设计完美融合，带来快捷流畅的震撼驾驭体验。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quadrant
msgid "Vehicle Services"
msgstr "车辆服务"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_picture
msgid "View the all-new KORAN in 3D wherever you are"
msgstr "随时随地观看全新 3D 版《古兰经》"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid ""
"We offer personalized vehicle configurations to match your preferences and "
"needs, ensuring you get the perfect fit for your lifestyle."
msgstr "我们提供个性化的车辆配置来满足您的喜好和需求，确保您获得最适合您生活方式的配置。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid ""
"We offer the latest advancements in automotive technology and design. "
"Leveraging cutting-edge features, we ensure every vehicle provides "
"exceptional performance and sophistication."
msgstr "我们提供最先进的汽车技术和设计。我们利用最先进的功能，确保每辆车都具有卓越性能和精致外观。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid "We proudly serve over 300,000 satisfied customers."
msgstr "我们自豪地为 300,000 多名客户提供满意的服务。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid ""
"We provide tailored luxury vehicles designed to meet your specific "
"preferences."
msgstr "我们提供量身定制的豪华车，以满足您的特定喜好。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "What we offer to our clients"
msgstr "我们为客户提供的服务"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid "Wide Vehicle Selection"
msgstr "广泛的车辆选择"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid ""
"With extensive experience and superior craftsmanship, we provide high-end "
"vehicles that deliver unmatched performance and style, ensuring a luxurious "
"driving experience."
msgstr "凭借丰富的经验和精湛的工艺，我们提供的高端汽车具有无与伦比的性能和风格，确保为您带来奢华的驾驶体验。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_grid
msgid "Your advantages"
msgstr "你的优势"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Your satisfaction and environmental impact are our priorities."
msgstr "您的满意和对环境的影响是我们的首要任务。"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_big_number
msgid "customer satisfaction"
msgstr "客户满意度"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "✽  Customization &amp; Upgrades"
msgstr "✽  定制和升级"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "✽  Maintenance &amp; Care"
msgstr "✽  维护和保养"
