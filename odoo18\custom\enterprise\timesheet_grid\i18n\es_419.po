# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* timesheet_grid
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:44+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_kanban
msgid "<i class=\"fa fa-pause text-warning\" title=\"Timer is Paused\"/>"
msgstr ""
"<i class=\"fa fa-pause text-warning\" title=\"El temporizador está "
"pausado\"/>"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_kanban
msgid "<i class=\"fa fa-play text-success\" title=\"Timer is Running\"/>"
msgstr "<i class=\"fa fa-play text-success\" title=\"El temporizador está activo\"/>"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid "<span class=\"col-auto ms-2\">min</span>"
msgstr "<span class=\"col-auto ms-2\">min</span>"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_gantt_timesheet
msgid "<strong>Time Spent — </strong>"
msgstr "<strong>Tiempo utilizado — </strong>"

#. module: timesheet_grid
#: model:mail.template,body_html:timesheet_grid.mail_template_timesheet_reminder_user
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Timesheets</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Abigail Peterson</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <t t-set=\"timesheet_hours\" t-value=\"ctx.get('timesheet_hours', 0)\"/>\n"
"                    <t t-set=\"working_hours\" t-value=\"ctx.get('working_hours', 0)\"/>\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or ''\">Abigail Peterson</t>,<br/><br/>\n"
"                        This is a friendly reminder to log your hours for the following period: <t t-out=\"ctx.get('date_start') or ''\">05/05/2021</t> <i class=\"fa fa-long-arrow-right\"/> <t t-out=\"ctx.get('date_stop') or ''\">05/06/2021</t>.\n"
"                        For the time being, you <t t-if=\"timesheet_hours != 0\">only</t> logged <t t-out=\"'%d' %int(timesheet_hours)\">2</t><t t-if=\"timesheet_hours % 1 != 0\" t-out=\"':%02d' % (round(timesheet_hours % 1 * 60))\">:30</t> hours on the <t t-out=\"'%d' %int(working_hours)\">8</t><t t-if=\"working_hours % 1 != 0\" t-out=\"':%02d' % (round(working_hours % 1 * 60))\">:30</t> requested.<br/>\n"
"                        <div t-if=\"ctx.get('action_url')\" style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"ctx.get('action_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 13px;\">Fill in your timesheet</a>\n"
"                        </div>\n"
"                        <br/>Thank you,<br/>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- ENCABEZADO -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Sus hojas de horas</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Abigail Peterson</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENIDO -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <t t-set=\"timesheet_hours\" t-value=\"ctx.get('timesheet_hours', 0)\"/>\n"
"                    <t t-set=\"working_hours\" t-value=\"ctx.get('working_hours', 0)\"/>\n"
"                    <div>\n"
"                        Hola <t t-out=\"object.name or ''\">Abigail Peterson</t>,<br/><br/>\n"
"                        Recuerde que debe registrar sus horas para el siguiente periodo: <t t-out=\"ctx.get('date_start') or ''\">05/05/2021</t> <i class=\"fa fa-long-arrow-right\"/> <t t-out=\"ctx.get('date_stop') or ''\">05/06/2021</t>.\n"
"                        Hasta ahora <t t-if=\"timesheet_hours != 0\">solo</t> ha registrado <t t-out=\"'%d' %int(timesheet_hours)\">2</t><t t-if=\"timesheet_hours % 1 != 0\" t-out=\"':%02d' % (round(timesheet_hours % 1 * 60))\">:30</t> horas de las <t t-out=\"'%d' %int(working_hours)\">8</t><t t-if=\"working_hours % 1 != 0\" t-out=\"':%02d' % (round(working_hours % 1 * 60))\">:30</t> necesarias.<br/>\n"
"                        <div t-if=\"ctx.get('action_url')\" style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"ctx.get('action_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 13px;\">Complete sus hojas de horas</a>\n"
"                        </div>\n"
"                        <br/>Gracias,<br/>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- PIE DE PÁGINA -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">SuEmpresa</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.ejemplo.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- CON LA TECNOLOGÍA DE -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Con la tecnologia de <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: timesheet_grid
#: model:mail.template,body_html:timesheet_grid.mail_template_timesheet_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Timesheets</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Abigail Peterson</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or ''\">Abigail Peterson</t>,<br/><br/>\n"
"                        This is a friendly reminder to approve your team's timesheets for the following period: <t t-out=\"ctx.get('date_start') or ''\">05/05/2021</t> - <t t-out=\"ctx.get('date_stop') or ''\">06/05/2021</t>.<br/>\n"
"                        <div t-if=\"ctx.get('action_url')\" style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"ctx.get('action_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 13px;\">Validate the Timesheets</a>\n"
"                        </div>\n"
"                        <br/>Thank you,<br/>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- ENCABEZADO -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Sus hojas de horas</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Abigail Peterson</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENIDO -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Hola <t t-out=\"object.name or ''\">Abigail Peterson</t>,<br/><br/>\n"
"                        Recuerde que debe aprobar las hojas de horas de su equipo del <t t-out=\"ctx.get('date_start') or ''\">05/05/2021</t> al <t t-out=\"ctx.get('date_stop') or ''\">06/05/2021</t>.<br/>\n"
"                        <div t-if=\"ctx.get('action_url')\" style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"ctx.get('action_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 13px;\">Valide las hojas de horas</a>\n"
"                        </div>\n"
"                        <br/>Gracias,<br/>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- PIE DE PÁGINA -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">SuEmpresa</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.ejemplo.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- CON LA TECNOLOGÍA DE -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Con la tecnologia de <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "A timer is running in another company."
msgstr "Un temporizador está corriendo en otra empresa. "

#. module: timesheet_grid
#: model:ir.ui.menu,name:timesheet_grid.menu_timesheet_grid_validate_all_timesheets
msgid "All Timesheets"
msgstr "Todas las hojas de horas"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__amount
msgid "Amount"
msgstr "Importe"

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "An employee must be linked to your user to record time."
msgstr "El empleado debe estar vinculado a un usuario para registrar tiempo."

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_account_analytic_line
msgid "Analytic Line"
msgstr "Línea analítica"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__reminder_allow
msgid "Approver Reminder"
msgstr "Recordatorio para el aprobador"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__reminder_interval
msgid "Approver Reminder Frequency"
msgstr "Frecuencia del recordatorio para el aprobador "

#. module: timesheet_grid
#: model_terms:ir.actions.act_window,help:timesheet_grid.timesheet_grid_to_validate_action
#: model_terms:ir.actions.act_window,help:timesheet_grid.timesheet_grid_to_validate_all_timesheets_action
msgid ""
"Check that your employees correctly filled in their timesheets and that "
"their time is billable."
msgstr ""
"Revise que sus empleados hayan llenado sus hojas de horas correctamente y "
"que se puedan facturar."

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid ""
"Click on the cell to set the number of hours you spent on this project."
msgstr ""
"Haga clic en la celda para establecer el número de horas que ha dedicado a "
"este proyecto."

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: timesheet_grid
#: model:mail.template,description:timesheet_grid.mail_template_timesheet_reminder
msgid ""
"Configure reminders in timesheet settings to remind approvers to validate "
"timesheets"
msgstr ""
"Configure recordatorios en las hojas de horas para que los aprobadores "
"recuerden validarlas. "

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/task.py:0
msgid "Confirm Time Spent"
msgstr "Confirmar el tiempo utilizado"

#. module: timesheet_grid
#: model_terms:web_tour.tour,rainbow_man_message:timesheet_grid.timesheet_tour
msgid "Congratulations, you are now a master of Timesheets."
msgstr "Felicidades, ya es un experto en hojas de horas."

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_project_task_create_timesheet
msgid "Create Timesheet from task"
msgstr "Crear hoja de horas desde una tarea"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__create_uid
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__create_date
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__create_date
msgid "Created on"
msgstr "Creado el"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/views/timer_timesheet_grid/timer_timesheet_grid_renderer.xml:0
msgid "Daily overtime"
msgstr "Horas extras diarias "

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__date
msgid "Date"
msgstr "Fecha"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_employee
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_project
msgid "Day"
msgstr "Día"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__reminder_delay
msgid "Days to Remind Approver"
msgstr "Días para recordar al aprobador "

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__reminder_user_delay
msgid "Days to Remind User"
msgstr "Días para recordar al usuario"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_create_timesheet_view_form
msgid "Delete"
msgstr "Eliminar"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_merge_wizard_view_form
msgid "Describe your activity"
msgstr "Describa su actividad"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid ""
"Describe your activity <i>(e.g. sent an e-mail, meeting with the "
"customer...)</i>."
msgstr ""
"Describa su actividad <i>(por ejemplo, enviar un correo, reunirse con el "
"cliente...)</i>."

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/grid_timesheet_timer_header/grid_timesheet_timer_header.js:0
#: code:addons/timesheet_grid/static/src/hooks/timesheet_timer_hooks.js:0
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_create_timesheet_view_form
msgid "Describe your activity..."
msgstr "Describa su actividad..."

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__name
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__description
msgid "Description"
msgstr "Descripción"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_overtime_indication/timesheet_overtime_indication.js:0
msgid ""
"Difference between the allocated %(uom)s (%(allocated_hours)s) and the "
"%(uom)s spent (%(worked_hours)s) on the project"
msgstr ""
"Diferencia entre las %(uom)s (%(allocated_hours)s) asignadas y las %(uom)s "
"utilizadas (%(worked_hours)s) en el proyecto"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_overtime_indication/timesheet_overtime_indication.js:0
msgid ""
"Difference between the allocated %(uom)s (%(allocated_hours)s) and the "
"%(uom)s spent (%(worked_hours)s) on the task"
msgstr ""
"Diferencia entre las %(uom)s (%(allocated_hours)s) asignadas y las %(uom)s "
"utilizadas (%(worked_hours)s) en la tarea"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/employee_overtime_indication/employee_overtime_indication.xml:0
msgid ""
"Difference between the number of hours recorded and the number of hours the "
"employee was supposed to work according to his contract"
msgstr ""
"Diferenciar entre el número de horas registradas y el número de horas que el"
" empleado debe trabajar de acuerdo con su contacto."

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_overtime_indication/timesheet_overtime_indication.js:0
msgid "Difference between the time allocated and the time recorded"
msgstr "Diferencia entre el tiempo asignado y el tiempo registrado."

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_merge_wizard_view_form
msgid "Discard"
msgstr "Descartar"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__display_name
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: timesheet_grid
#: model:ir.model.fields.selection,name:timesheet_grid.selection__account_analytic_line__validated_status__draft
#: model:ir.model.fields.selection,name:timesheet_grid.selection__timesheets_analysis_report__validated_status__draft
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_search
msgid "Draft"
msgstr "Borrador"

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_hr_employee
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__employee_id
msgid "Employee"
msgstr "Empleado"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__reminder_user_allow
msgid "Employee Reminder"
msgstr "Recordatorio para empleados"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "Enter"
msgstr "Enter"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid "Frequency"
msgstr "Frecuencia"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/views/timesheet_grid/timesheet_grid_search_model.js:0
msgid "Grouping by date is not supported"
msgstr "No se puede agrupar por fecha"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__id
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__id
msgid "ID"
msgstr "ID"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/views/timesheet_grid/timesheet_grid_renderer.js:0
msgid ""
"Keep track of your working hours by project every day and bill your "
"customers for that time."
msgstr ""
"Lleve el registro de sus horas trabajadas por proyecto cada día y facture "
"este tiempo a sus clientes."

#. module: timesheet_grid
#: model:ir.ui.menu,name:timesheet_grid.menu_timesheet_grid_validate_previous_week
msgid "Last Period"
msgstr "Último periodo"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__write_uid
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__write_date
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_employee__last_validated_timesheet_date
msgid "Last Validated Timesheet Date"
msgstr "Fecha de la última hoja de horas que se validó"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid ""
"Launch the <b>timer</b> for this project by pressing the <b>[a] key</b>. "
"Easily switch from one project to another by using those keys. <i>Tip: you "
"can also directly add 15 minutes to this project by hitting the <b>shift + "
"[A] keys</b>.</i>"
msgstr ""
"De clic en la <b>tecla [a]</b> para iniciar el <b>temporizador</b> para este"
" proyecto. <i>Con esas teclas, puede cambiar de un proyecto a otro con "
"facilidad. Consejo: si da clic a las <b>teclas shift + [A]</b> puede agregar"
" 15 minutos a este proyecto directamente.</i>"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid "Launch the <b>timer</b> to start a new activity."
msgstr "Inicie el <b>temporizador</b> para empezar una nueva actividad. "

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_create_timesheet_view_form
msgid "Log Time"
msgstr "Registrar tiempo"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_merge_wizard_view_form
msgid "Merge"
msgstr "Fusionar"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_merge_wizard_view_form
msgid "Merge Timesheet"
msgstr "Fusionar hoja de horas"

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
#: model:ir.actions.server,name:timesheet_grid.merge_timesheet_action
#: model:ir.model,name:timesheet_grid.model_hr_timesheet_merge_wizard
msgid "Merge Timesheets"
msgstr "Fusionar hojas de horas"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__timesheet_min_duration
msgid "Minimal Duration"
msgstr "Duración mínima"

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_ir_module_module
msgid "Module"
msgstr "Módulo"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_employee
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_project
msgid "Month"
msgstr "Mes"

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/task.py:0
msgid ""
"Moving this task to a project without timesheet support will retain "
"timesheet drafts in the original project. Although they won't be visible "
"here, you can still edit them using the Timesheets app."
msgstr ""
"Mover esta tarea a un proyecto sin compatibilidad con hojas de horas "
"mantendrá los borradores de las hojas en el proyecto original. Aunque no "
"serán visibles aquí, puede editarlas con la aplicación Hojas de horas."

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_search
msgid "My Department"
msgstr "Mi departamento"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_search
msgid "My Projects"
msgstr "Mis proyectos"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_search
msgid "My Tasks"
msgstr "Mis tareas"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_search
msgid "My Team"
msgstr "Mi equipo"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/views/timesheet_grid/timesheet_grid_renderer.js:0
msgid "No timesheets found. Let's create one!"
msgstr "No se encontraron hojas de horas, creemos una."

#. module: timesheet_grid
#: model_terms:ir.actions.act_window,help:timesheet_grid.timesheet_grid_to_validate_action
#: model_terms:ir.actions.act_window,help:timesheet_grid.timesheet_grid_to_validate_all_timesheets_action
msgid "No timesheets to validate"
msgstr "No hay hojas de hora por validar"

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_res_config_settings__reminder_delay
msgid ""
"Number of days after the end of the week/month after which an automatic "
"email reminder will be sent to timesheet managers that still have timesheets"
" to validate."
msgstr ""
"Número de días después del final de la semana o del mes que tienen que pasar"
" para que se envíe un correo de recordatorio automático a los gerentes que "
"todavía tienen hojas de horas por validar."

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_res_config_settings__reminder_user_delay
msgid ""
"Numbers of days after the end of the week/month after which an automatic "
"email reminder will be sent to timesheet users that still have timesheets to"
" encode (according to their working hours)."
msgstr ""
"Número de días después del final de la semana o del mes que tienen que pasar"
" para que se envíe un correo de recordatorio automático a los usuarios que "
"todavía tienen que registrar hojas de horas (según sus horas laborales)."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid ""
"Only a Timesheets Approver or Manager is allowed to modify a validated "
"entry."
msgstr ""
"Solo una persona que pueda aprobar las hojas de horas o un gerente puede "
"modificar una entrada que ya esté validada."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "Open Form"
msgstr "Abrir formulario"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_form
msgid "Pause"
msgstr "Pausa"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "Press"
msgstr "Presione"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "Press Esc to discard"
msgstr "Presione Esc para descartar"

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_project_project
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__project_id
msgid "Project"
msgstr "Proyecto"

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_hr_employee_public
msgid "Public Employee"
msgstr "Empleado público"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__unit_amount
msgid "Quantity"
msgstr "Cantidad"

#. module: timesheet_grid
#: model:mail.template,subject:timesheet_grid.mail_template_timesheet_reminder_user
msgid "Reminder to log your timesheets"
msgstr "Recuerde que debe registrar sus hojas de horas"

#. module: timesheet_grid
#: model:mail.template,subject:timesheet_grid.mail_template_timesheet_reminder
msgid "Reminder to validate your team's timesheets"
msgstr "Recuerde que debe validar las hojas de horas de su equipo"

#. module: timesheet_grid
#: model:ir.actions.server,name:timesheet_grid.invalidate_timesheet_action
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_form_view
msgid "Reset to draft"
msgstr "Restablecer a borrador"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_form
msgid "Resume"
msgstr "Reanudar"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_create_timesheet_view_form
msgid "Resume Timer"
msgstr "Reanudar temporizador"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__timesheet_rounding
msgid "Round up"
msgstr "Redondear"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid "Rounding applied when tracking your time using the timer"
msgstr ""
"Redondeo que se aplica cuando registra su tiempo usando el temporizador"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_create_timesheet_view_form
msgid "Save time"
msgstr "Guardar tiempo"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid "Select the <b>project</b> on which you are working."
msgstr "Seleccione el <b>proyecto</b> en el que está trabajando."

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_hr_employee__timesheet_manager_id
#: model:ir.model.fields,help:timesheet_grid.field_res_users__timesheet_manager_id
msgid ""
"Select the user responsible for approving \"Timesheet\" of this employee.\n"
"If empty, the approval is done by a Timesheets > Administrator or a Timesheets > User: all timesheets (as determined in the users settings)."
msgstr ""
"Seleccione al usuario que será responsable de aprobar las \"hojas de horas\" de este empleado.\n"
"Si está vacío, se puede configurar en Hojas de horas > Administrador o en Hoja de horas > Usuario: todas las hojas de horas (como está determinado en los ajustes del usuario). "

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_account_analytic_line__is_timesheet
msgid "Set if this analytic line represents a line of timesheet."
msgstr ""
"Establecer si esta línea analítica representa una línea de la hoja de horas."

#. module: timesheet_grid
#: model:mail.template,description:timesheet_grid.mail_template_timesheet_reminder_user
msgid ""
"Set reminders in settings to notify employees who didn't record their "
"timesheet"
msgstr ""
"Configurar un recordatorio en los ajustes para notificar a los empleados que"
" no han registrado sus hojas de horas."

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid ""
"Set the number of hours you spent on this project (e.g. 1:30 or 1.5). "
"<i>Tip: use the tab keys to easily navigate from one cell to another.</i>"
msgstr ""
"Indique el número de horas que trabajará en este proyecto (p. ej. 1:30 o "
"1.5). <i>Consejo: use las teclas tab para navegar de una celda a otra "
"fácilmente. </i>"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "Shift"
msgstr "Shift"

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "Sorry, you cannot use a timer for a validated timesheet"
msgstr ""
"Lo sentimos, no puede usar el temporizador en una hoja de horas que ya se "
"validó."

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
#: code:addons/timesheet_grid/static/src/components/timesheet_uom_hour_timer/timesheet_uom_hour_timer.js:0
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_form
msgid "Start"
msgstr "Iniciar"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
#: code:addons/timesheet_grid/static/src/components/timesheet_uom_hour_timer/timesheet_uom_hour_timer.js:0
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_form
msgid "Stop"
msgstr "Detener"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid ""
"Stop the <b>timer</b> when you are done. <i>Tip: hit <b>[Enter]</b> in the "
"description to automatically log your activity.</i>"
msgstr ""
"Detenga el <b>temporizador</b> cuando termine. <i>Consejo: presione "
"<b>[Enter]</b> desde la descripción para registrar su actividad en "
"automático.</i>"

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_project_task
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__task_id
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__task_id
msgid "Task"
msgstr "Tarea"

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_project_task_create_timesheet__task_id
msgid "Task for which we are creating a sales order"
msgstr "Tareas para las que estamos creando una orden de venta"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid ""
"The duration of timesheets encoded through the timer will automatically be "
"rounded up to this number. For instance, if you stop your timer at 00:16:56,"
" the duration of the timesheet entry will automatically be rounded up to "
"00:30 (assuming you have a round up of 15 min). We recommend having the same"
" value for the minimal duration and for the round up."
msgstr ""
"La duración de las hojas de horas que se hayan registrado mediante el "
"temporizador se redondearán automáticamente a este número. Por ejemplo, si "
"detiene el temporizador cuando el tiempo sea 00:16:56 la duración en la hoja"
" de horas se redondeará a 00:30 (suponiendo que eligió redondear por 15 "
"minutos). Le recomendamos que la cantidad que tenga en la duración mínima y "
"en el redondeo sea la misma."

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/views/timesheet_grid/timesheet_grid_controller.js:0
msgid "The timesheet entry has successfully been created."
msgstr "Se creó el registro para la hoja de hora con éxito."

#. module: timesheet_grid
#: model:ir.model.constraint,message:timesheet_grid.constraint_project_task_create_timesheet_time_positive
msgid "The timesheet's time must be positive"
msgstr "El tiempo de la hoja de horas debe ser positivo"

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/wizard/timesheet_merge_wizard.py:0
msgid "The timesheets have successfully been merged."
msgstr "La hojas de hora se mezclaron con éxito."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "The timesheets have successfully been reset to draft."
msgstr "Las hojas de hora se regresaron a borrador."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "The timesheets have successfully been validated."
msgstr "Las hojas de horas se validaron exitosamente"

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/wizard/timesheet_merge_wizard.py:0
msgid "The timesheets must have the same encoding unit"
msgstr "Las hojas de horas deben tener la misma unidad de codificación"

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "There are no timesheets to merge."
msgstr "No hay hojas de horas para mezclar."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid ""
"There are no timesheets to reset to draft or they have already been "
"invoiced."
msgstr ""
"No hay hojas de horas que restablecer a borradores o ya se facturaron."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/task.py:0
msgid "This project isn't expected to have task during this period."
msgstr "No se espera que este proyecto tenga una tarea durante este periodo."

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__time_spent
msgid "Time"
msgstr "Tiempo"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid "Time Rounding"
msgstr "Redondeo del tiempo"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_create_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_kanban_view
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_merge_wizard_view_form
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_employee
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_project
msgid "Time Spent"
msgstr "Tiempo utilizado"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_employee__timesheet_manager_id
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_employee_public__timesheet_manager_id
#: model:ir.model.fields,field_description:timesheet_grid.field_res_users__timesheet_manager_id
msgid "Timesheet"
msgstr "Hoja de horas"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.view_employee_tree_inherit_timesheet
msgid "Timesheet Approver"
msgstr "Persona que aprueba las hojas de horas"

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_timesheet_grid_mixin
msgid "Timesheet Grid mixin"
msgstr "Mixin de tabla de hojas de horas"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__is_timesheet
#: model:ir.model.fields,field_description:timesheet_grid.field_timesheets_analysis_report__is_timesheet
msgid "Timesheet Line"
msgstr "Línea hoja de horas"

#. module: timesheet_grid
#: model:ir.actions.server,name:timesheet_grid.timesheet_reminder_ir_actions_server
msgid "Timesheet: Approvers Email Reminder"
msgstr ""
"Hojas de horas: Recordatorio mediante correo electrónico para aprobadores"

#. module: timesheet_grid
#: model:ir.actions.server,name:timesheet_grid.timesheet_reminder_user_ir_actions_server
msgid "Timesheet: Employees Email Reminder"
msgstr ""
"Hojas de horas: Recordatorio mediante correo electrónico para empleados"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_employee
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_project
msgid "Timesheets"
msgstr "Hojas de horas"

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr "Reporte de análisis de hoja de horas"

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid ""
"Timesheets before the %(date)s (included) have been validated, and can no "
"longer be deleted."
msgstr ""
"Las hojas de horas antes del %(date)s (incluida) se validaron y ya no es "
"posible eliminarlas."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid ""
"Timesheets before the %(date)s (included) have been validated, and can no "
"longer be modified."
msgstr ""
"Las hojas de horas antes del %(date)s (incluida) se validaron y ya no es "
"posible modificarlas."

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid ""
"Timesheets encoded via the timer that do not meet the minimal duration will "
"automatically be rounded up to the defined value. For instance, if you stop "
"your timer at 00:04:36, the duration of the timesheet entry will "
"automatically be rounded up to 00:15 (assuming you have a minimal duration "
"of 15 min). We recommend having the same value for the minimal duration and "
"for the round up."
msgstr ""
"Las hojas de horas que registre con el temporizador y no cumplan con la "
"duración mínima se redondearán para que lleguen al valor definido. Por "
"ejemplo, si para su temporizador en 00:04:36, la duración de la hoja de "
"horas se redondeará automáticamente a 00:15 minutos (asumiendo que la "
"duración mínima es de 15 minutos). Le recomendamos que la cantidad que tenga"
" en la duración mínima y en el redondeo sea la misma."

#. module: timesheet_grid
#: model:ir.actions.act_window,name:timesheet_grid.timesheet_grid_to_validate_action
#: model:ir.actions.act_window,name:timesheet_grid.timesheet_grid_to_validate_all_timesheets_action
msgid "Timesheets to Validate"
msgstr "Hojas de horas por validar"

#. module: timesheet_grid
#: model:mail.template,name:timesheet_grid.mail_template_timesheet_reminder
msgid "Timesheets: Approver Reminder"
msgstr "Hojas de horas: Recordatorio de aprobador"

#. module: timesheet_grid
#: model:mail.template,name:timesheet_grid.mail_template_timesheet_reminder_user
msgid "Timesheets: Employee Reminder"
msgstr "Hojas de horas: Recordatorio de empleados"

#. module: timesheet_grid
#: model:ir.ui.menu,name:timesheet_grid.menu_timesheet_grid_validate
msgid "To Validate"
msgstr "Por validar"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid "Track the <b>time spent</b> on your projects. <i>It starts here.</i>"
msgstr ""
"Supervise el <b>tiempo que invierte</b> en sus proyectos. <i>Todo empieza "
"aquí.</i>"

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_res_users
msgid "User"
msgstr "Usuario"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__user_can_validate
msgid "User Can Validate"
msgstr "El usuario puede validar"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__reminder_user_interval
msgid "User Reminder Frequency"
msgstr "Frecuencia de recordatorio al usuario"

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_hr_employee_public__timesheet_manager_id
msgid "User responsible of timesheet validation. Should be Timesheet Manager."
msgstr ""
"Usuario responsable de la validación de las hojas de horas. Debería ser un "
"gerente de hojas de horas."

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/views/timesheet_pivot/timesheet_validation_pivot_controller.xml:0
#: code:addons/timesheet_grid/static/src/views/timesheet_kanban/timesheet_validation_kanban_controller.xml:0
#: model:ir.actions.server,name:timesheet_grid.timesheet_validate_action
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_form_view
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_employee_validation
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_tree_user_inherited
msgid "Validate"
msgstr "Validar "

#. module: timesheet_grid
#: model:ir.model.fields.selection,name:timesheet_grid.selection__account_analytic_line__validated_status__validated
#: model:ir.model.fields.selection,name:timesheet_grid.selection__timesheets_analysis_report__validated_status__validated
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_search
msgid "Validated"
msgstr "Validado"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__validated_status
#: model:ir.model.fields,field_description:timesheet_grid.field_timesheets_analysis_report__validated_status
msgid "Validated Status"
msgstr "Estado validado"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__validated
#: model:ir.model.fields,field_description:timesheet_grid.field_timesheets_analysis_report__validated
msgid "Validated line"
msgstr "Línea validada"

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/task.py:0
msgid "Warning"
msgstr "Advertencia"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_employee
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_project
msgid "Week"
msgstr "Semana"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/views/timer_timesheet_grid/timer_timesheet_grid_renderer.xml:0
msgid "Weekly overtime"
msgstr "Horas extras semanales"

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_account_analytic_line__user_can_validate
msgid ""
"Whether or not the current user can validate/reset to draft the record."
msgstr "Si el usuario actual puede validar/resetear a borrador el registro."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/hr_employee.py:0
#: code:addons/timesheet_grid/models/res_users.py:0
msgid "You are not allowed to see timesheets."
msgstr "No tiene permitido ver las hojas de horas."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid ""
"You can only reset to draft the timesheets of employees of whom you are the "
"manager or the timesheet approver."
msgstr ""
"Solo puede resetear a borradores las hojas de horas de los empleados de los "
"que es el gerente o la persona quien aprueba las hojas de horas."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid ""
"You can only validate the timesheets of employees of whom you are the "
"manager or the timesheet approver."
msgstr ""
"Solo puede validar las hojas de horas de los empleados de los que es el "
"gerente."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "You can't encode numbers with more than six digits."
msgstr "No puede codificar números con más de seis dígitos."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "You cannot access timesheets that are not yours."
msgstr "No puede acceder a hojas de horas que no son suyas."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid ""
"You cannot adjust the time of the timesheet for a project with timesheets "
"disabled."
msgstr ""
"No puede ajustar el tiempo de una hoja de horas para un proyecto con las "
"hojas de horas desactivadas. "

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid ""
"You cannot delete a validated entry. Please contact your manager or your "
"timesheet approver."
msgstr ""
"No puede eliminar una entrada validada. Contacte a su gerente o a quien "
"aprueba sus hojas de horas."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/project.py:0
msgid ""
"You cannot start the timer for a project in a company encoding its "
"timesheets in days."
msgstr ""
"No puede empezar el temporizador para un proyecto en una empresa que "
"codifica las hojas de horas en días."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "You cannot use the timer on validated timesheets."
msgstr "No puede usar el temporizador en hojas de horas validadas"

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid ""
"You cannot validate the selected timesheets as they either belong to "
"employees who are not part of your team or are not in a state that can be "
"validated. This may be due to the fact that they are dated in the future."
msgstr ""
"No puede validar las hojas de horas seleccionadas, pertenecen a empleados "
"que no forman parte de su equipo o no están en un estado que pueda "
"validarse. Puede ser debido a que su fecha se encuentra en el futuro."

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid ""
"Your timesheet entry is missing a project. Please either group the Grid view"
" by project or enter your timesheets by adding a line via the Form view."
msgstr ""
"Falta un proyecto en su hoja de horas. Agrupe por proyecto con la vista de "
"tabla o ingrese sus hojas de horas agregando una línea a través de la vista "
"de formulario."

#. module: timesheet_grid
#: model:ir.model.fields.selection,name:timesheet_grid.selection__res_company__timesheet_mail_employee_interval__months
#: model:ir.model.fields.selection,name:timesheet_grid.selection__res_company__timesheet_mail_interval__months
msgid "after the end of the month"
msgstr "después de fin del mes"

#. module: timesheet_grid
#: model:ir.model.fields.selection,name:timesheet_grid.selection__res_company__timesheet_mail_employee_interval__weeks
#: model:ir.model.fields.selection,name:timesheet_grid.selection__res_company__timesheet_mail_interval__weeks
msgid "after the end of the week"
msgstr "después del fin de la semana"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "and click on"
msgstr "y haga clic en"

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid "days"
msgstr "días"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "min"
msgstr "min"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "or"
msgstr "o"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "or click on"
msgstr "o haga clic en"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "to add"
msgstr "para agregar"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "to add 15 min"
msgstr "para agregar 15 minutos"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "to launch the timer"
msgstr "para iniciar el temporizador"
