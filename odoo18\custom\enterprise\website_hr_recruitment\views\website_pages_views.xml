<?xml version="1.0"?>
<odoo>

<record id="job_pages_tree_view" model="ir.ui.view">
    <field name="name">Job Pages List</field>
    <field name="model">hr.job</field>
    <field name="priority">99</field>
    <field name="mode">primary</field>
    <field name="inherit_id" ref="hr.view_hr_job_tree"/>
    <field name="arch" type="xml">
        <xpath expr="//list" position="attributes">
            <attribute name="js_class">website_pages_list</attribute>
            <attribute name="type">object</attribute>
            <attribute name="action">open_website_url</attribute>
            <attribute name="multi_edit">1</attribute>
        </xpath>

        <field name="name" position="after">
            <field name="website_url"/>
            <field name="company_id" column_invisible="True"/>
        </field>
        <xpath expr="//list">
            <field name="is_seo_optimized"/>
            <field name="is_published" position="move"/>

            <field name="website_id" groups="website.group_multi_website"/>
        </xpath>
    </field>
</record>

<record id="job_pages_kanban_view" model="ir.ui.view">
    <field name="name">Job Pages Kanban</field>
    <field name="model">hr.job</field>
    <field name="priority">99</field>
    <field name="mode">primary</field>
    <field name="inherit_id" ref="hr_job_website_inherit"/>
    <field name="arch" type="xml">
        <kanban position="attributes">
            <attribute name="js_class">website_pages_kanban</attribute>
            <attribute name="type">object</attribute>
            <attribute name="action">open_website_url</attribute>
        </kanban>
        <xpath expr="//div[hasclass('o_kanban_card_header_title')]" position="inside">
            <div class="text-muted fw-bold ps-3">
                <span class="me-3" t-if="record.website_id.value" groups="website.group_multi_website">
                    <i class="fa fa-globe me-1" title="Website"/>
                    <field name="website_id"/>
                </span>
                <field name="is_seo_optimized" widget="boolean"/> SEO Optimized
            </div>
        </xpath>
        <xpath expr="//div[hasclass('o_link_trackers')]" position="replace"/>
    </field>
</record>

<record id="action_job_pages_list" model="ir.actions.act_window">
    <field name="name">Job Pages</field>
    <field name="res_model">hr.job</field>
    <field name="view_mode">list,kanban,form</field>
    <field name="view_ids" eval="[(5, 0, 0),
        (0, 0, {'view_mode': 'list', 'view_id': ref('job_pages_tree_view')}),
        (0, 0, {'view_mode': 'kanban', 'view_id': ref('job_pages_kanban_view')}),
    ]"/>
    <field name="context">{'create_action': '/jobs/add'}</field>
</record>

<menuitem id="menu_job_pages"
    parent="website.menu_content"
    sequence="70"
    name="Jobs"
    action="action_job_pages_list"
    groups="hr_recruitment.group_hr_recruitment_interviewer"/>

</odoo>
