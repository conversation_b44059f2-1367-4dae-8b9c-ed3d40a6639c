<?xml version="1.0" encoding="UTF-8"?>
<odoo noupdate="1">

        <!-- Update existing rule : Employee can not modify validated timesheets -->
        <record id="hr_timesheet.timesheet_line_rule_user" model="ir.rule">
            <field name="name">account.analytic.line.timesheet.user</field>
            <field name="model_id" ref="analytic.model_account_analytic_line"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="timesheet_line_rule_user_update-unlink" model="ir.rule">
            <field name="name">account.analytic.line.timesheet.user.update-unlink</field>
            <field name="model_id" ref="analytic.model_account_analytic_line"/>
            <field name="domain_force">[
                ('user_id', '=', user.id),
                ('validated', '=', False),
                ('project_id', '!=', False),
                '|',
                    ('project_id.privacy_visibility', '!=', 'followers'),
                    ('message_partner_ids', 'in', [user.partner_id.id])
            ]</field>
            <field name="groups" eval="[(4, ref('hr_timesheet.group_hr_timesheet_user'))]"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_read" eval="0"/>
        </record>

</odoo>
