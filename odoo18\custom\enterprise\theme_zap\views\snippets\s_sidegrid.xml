<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_sidegrid" inherit_id="website.s_sidegrid">
    <!-- Images -->
    <xpath expr="//img" position="attributes">
        <attribute name="class" remove="rounded" separator=" "/>
    </xpath>
    <xpath expr="(//img)[2]" position="attributes">
        <attribute name="class" remove="rounded" separator=" "/>
    </xpath>
    <xpath expr="(//img)[3]" position="attributes">
        <attribute name="class" remove="rounded" separator=" "/>
    </xpath>
    <xpath expr="(//img)[4]" position="attributes">
        <attribute name="class" remove="rounded" separator=" "/>
    </xpath>
</template>

</odoo>
