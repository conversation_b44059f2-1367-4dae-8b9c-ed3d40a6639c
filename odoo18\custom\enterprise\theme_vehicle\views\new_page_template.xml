<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- About -->

<template id="new_page_template_sections_about_personal" inherit_id="website.new_page_template_sections_about_personal">
    <xpath expr="//t[@t-snippet-call='website.new_page_template_about_personal_s_call_to_action_about']" position="attributes">
        <attribute name="t-snippet-call">website.new_page_template_about_personal_s_call_to_action</attribute>
    </xpath>
</template>

<template id="new_page_template_sections_team_5" inherit_id="website.new_page_template_sections_team_5">
    <xpath expr="//t[@t-snippet-call='website.new_page_template_team_5_s_picture']" position="attributes">
        <attribute name="t-snippet-call">website.new_page_template_s_picture</attribute>
    </xpath>
</template>

<!-- General customizations -->

<template id="new_page_template_s_references" inherit_id="website.new_page_template_s_references">
    <xpath expr="//section" position="attributes">
        <attribute name="data-oe-shape-data">{"shape":"web_editor/Origins/05","flip":[]}</attribute>
    </xpath>
    <xpath expr="//div[hasclass('container')]" position="before">
        <div class="o_we_shape o_web_editor_Origins_05"/>
    </xpath>
</template>

<!-- Snippet customization Basic Pages -->

<template id="new_page_template_basic_2_s_text_block_h1" inherit_id="website.new_page_template_basic_2_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb40" remove="pb0" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization About Pages -->

<template id="new_page_template_about_s_banner" inherit_id="website.new_page_template_about_s_banner">
    <xpath expr="//section" position="attributes">
        <attribute name="data-oe-shape-data">{"shape":"web_editor/Origins/18","flip":["x"]}</attribute>
    </xpath>
    <xpath expr="//div[hasclass('container')]" position="before">
        <div class="o_we_shape o_web_editor_Origins_18" style="background-image: url('/web_editor/shape/web_editor/Origins/18.svg?c1=o-color-4&amp;flip=x'); background-position: 50% 50%;"/>
    </xpath>
</template>

<template id="new_page_template_about_s_cover" inherit_id="website.new_page_template_about_s_cover">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_full_screen_height" remove="o_half_screen_height" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_about_full_1_s_text_block_h1" inherit_id="website.new_page_template_about_full_1_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_cc3" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_about_full_s_image_text" inherit_id="website.new_page_template_about_full_s_image_text">
    <xpath expr="//section" position="attributes">
        <!-- pb56 is defined by both theme and new page template -->
        <attribute name="class" add="pb56" remove="pt24 pb56" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_about_full_s_numbers" inherit_id="website.new_page_template_about_full_s_numbers">
    <!-- Shape option -->
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="o_cc3" separator=" "/>
        <attribute name="data-oe-shape-data">{"shape":"web_editor/Rainy/09_001","flip":[]}</attribute>
    </xpath>
    <!-- Shape -->
    <xpath expr="//div[hasclass('container')]" position="before">
        <div class="o_we_shape o_web_editor_Rainy_09_001"/>
    </xpath>
</template>

<template id="new_page_template_about_full_s_text_image" inherit_id="website.new_page_template_about_full_s_text_image">
    <xpath expr="//section" position="attributes">
        <!-- pb56 is defined by both theme and new page template -->
        <attribute name="class" add="pt56" remove="pt56 pb24" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_about_map_s_numbers" inherit_id="website.new_page_template_about_map_s_numbers">
    <!-- Shape option -->
    <xpath expr="//section" position="attributes">
        <attribute name="data-oe-shape-data">{"shape":"web_editor/Rainy/09_001","flip":[]}</attribute>
    </xpath>
    <!-- Shape -->
    <xpath expr="//div[hasclass('container')]" position="before">
        <div class="o_we_shape o_web_editor_Rainy_09_001"/>
    </xpath>
</template>

<template id="new_page_template_about_mini_s_cover" inherit_id="website.new_page_template_about_mini_s_cover">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="pt0 pb40" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_about_personal_s_numbers" inherit_id="website.new_page_template_about_personal_s_numbers">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="o_cc4 pt0" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Landing Pages -->

<template id="new_page_template_landing_s_text_image" inherit_id="website.new_page_template_landing_s_text_image">
    <xpath expr="//section" position="attributes">
        <!-- pt56 is defined in both theme and new page template -->
        <attribute name="class" add="pt56" remove="pt56 pb24" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_s_cover" inherit_id="website.new_page_template_s_cover">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="o_half_screen_height" separator=" "/>
    </xpath>
</template>


<template id="new_page_template_landing_0_s_cover" inherit_id="website.new_page_template_landing_0_s_cover">
    <xpath expr="//section" position="attributes">
        <!-- pb256 is defined in both theme and new page template -->
        <attribute name="class" add="pb80 pt80" remove="pt256 pb256 pt0 pb0" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_landing_1_s_banner" inherit_id="website.new_page_template_landing_1_s_banner">
    <xpath expr="//section" position="attributes">
        <attribute name="data-oe-shape-data">{"shape":"web_editor/Origins/18","flip":["x"]}</attribute>
    </xpath>
    <xpath expr="//div[hasclass('container')]" position="before">
        <div class="o_we_shape o_web_editor_Origins_18" style="background-image: url('/web_editor/shape/web_editor/Origins/18.svg?c1=o-color-4&amp;flip=x'); background-position: 50% 50%;"/>
    </xpath>
</template>

<template id="new_page_template_landing_2_s_cover" inherit_id="website.new_page_template_landing_2_s_cover">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_full_screen_height" remove="o_half_screen_height" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_landing_2_s_text_block_h2" inherit_id="website.new_page_template_landing_2_s_text_block_h2">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="o_cc2" add="o_cc3" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_landing_3_s_text_block_h2" inherit_id="website.new_page_template_landing_3_s_text_block_h2">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="o_cc2" add="o_cc3" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Gallery Pages -->

<template id="new_page_template_gallery_s_banner" inherit_id="website.new_page_template_gallery_s_banner">
    <xpath expr="//section" position="attributes">
        <attribute name="data-oe-shape-data">{"shape":"web_editor/Origins/18","flip":["x"]}</attribute>
    </xpath>
    <xpath expr="//div[hasclass('container')]" position="before">
        <div class="o_we_shape o_web_editor_Origins_18" style="background-image: url('/web_editor/shape/web_editor/Origins/18.svg?c1=o-color-4&amp;flip=x'); background-position: 50% 50%;"/>
    </xpath>
</template>

<template id="new_page_template_gallery_s_cover" inherit_id="website.new_page_template_gallery_s_cover">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_full_screen_height" remove="o_half_screen_height" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_gallery_s_image_text_2nd" inherit_id="website.new_page_template_gallery_s_image_text_2nd">
    <xpath expr="//section" position="attributes">
        <!-- pb56 is defined in both theme and new page template -->
        <attribute name="class" add="pb56" remove="pt24 pb56" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Services Pages -->

<template id="new_page_template_services_s_text_image" inherit_id="website.new_page_template_services_s_text_image">
    <xpath expr="//section" position="attributes">
        <!-- pt56 is defined in both theme and new page template -->
        <attribute name="class" add="pt56" remove="pt56 pb24" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Pricing Pages -->

<template id="new_page_template_pricing_s_cover" inherit_id="website.new_page_template_pricing_s_cover">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_full_screen_height" remove="o_half_screen_height" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_pricing_s_image_text_2nd" inherit_id="website.new_page_template_pricing_s_image_text_2nd">
    <xpath expr="//section" position="attributes">
        <!-- pb56 is defined in both theme and new page template -->
        <attribute name="class" add="pb56" remove="pt24 pb56" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Team Pages -->

<template id="new_page_template_team_s_text_block_h1" inherit_id="website.new_page_template_team_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_cc3" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_team_s_text_image" inherit_id="website.new_page_template_team_s_text_image">
    <xpath expr="//section" position="attributes">
        <!-- pt56 is defined in both theme and new page template -->
        <attribute name="class" add="pt56" remove="pt56 pb24" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_team_1_s_text_block_h1" inherit_id="website.new_page_template_team_1_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb0" remove="o_cc3 pb40" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_team_2_s_text_block_h1" inherit_id="website.new_page_template_team_2_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="o_cc3" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_team_4_s_text_block_h1" inherit_id="website.new_page_template_team_4_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb0" remove="o_cc3 pb40" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_team_5_s_text_block_h1" inherit_id="website.new_page_template_team_5_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb0" remove="o_cc3 pb40" separator=" "/>
    </xpath>
</template>

</odoo>
