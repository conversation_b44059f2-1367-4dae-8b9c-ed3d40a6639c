# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_yes
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:30+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quotes_carousel_minimal
msgid ""
"\" A top choice for unforgettable weddings. <br/>Professional, creative, and"
" dedicated to every detail. \""
msgstr ""
"\" A top choice for unforgettable weddings. <br/>Professional, creative, and"
" dedicated to every detail. \""

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quotes_carousel_minimal
msgid ""
"\" Exceptional planning and execution! <br/>They made our wedding day truly "
"special and memorable. \""
msgstr ""
"\" Exceptional planning and execution! <br/>They made our wedding day truly "
"special and memorable. \""

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quotes_carousel_minimal
msgid ""
"\" Their service made our day magical. Elegant, seamless, and perfectly "
"tailored to our vision. \""
msgstr ""
"\" Their service made our day magical. Elegant, seamless, and perfectly "
"tailored to our vision. \""

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "$1,200.00"
msgstr "$1.200,00"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "$1,500.00"
msgstr "$1.500,00"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "$1,800.00"
msgstr "$1.800,00"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "$2,500.00"
msgstr "$2,500.00"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "$3,000.00"
msgstr "$3,000.00"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "$5,000.00"
msgstr "$5,000.00"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_big_number
msgid "1,500+"
msgstr "1,500+"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_text_cover
msgid ""
"<br/>Organize and showcase your wedding services with a user-friendly "
"platform that simplifies everything, from setup to event management, making "
"it easy for couples to discover and choose the perfect details for their big"
" day<br/>"
msgstr ""
"<br/>Organize and showcase your wedding services with a user-friendly "
"platform that simplifies everything, from setup to event management, making "
"it easy for couples to discover and choose the perfect details for their big"
" day<br/>"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_empowerment
msgid ""
"<br/>Turning your vision into a beautiful reality with personalized "
"touches<br/><br/>"
msgstr ""
"<br/>Turning your vision into a beautiful reality with personalized "
"touches<br/><br/>"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team
msgid ""
"<font style=\"font-weight: bolder;\" class=\"text-o-color-1\">The "
"Designer</font>"
msgstr ""
"<font style=\"font-weight: bolder;\" class=\"text-o-color-1\">The "
"Designer</font>"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team
msgid ""
"<font style=\"font-weight: bolder;\" class=\"text-o-color-1\">The "
"Planner</font>"
msgstr ""
"<font style=\"font-weight: bolder;\" class=\"text-o-color-1\">The "
"Planner</font>"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Dream "
"weddings"
msgstr ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Dream "
"weddings"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_features
msgid "<span class=\"h5-fs\">Capturing the moment</span>"
msgstr "<span class=\"h5-fs\">Capturing the moment</span>"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_features
msgid "<span class=\"h5-fs\">Curating the finest menu</span>"
msgstr "<span class=\"h5-fs\">Curating the finest menu</span>"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_features
msgid "<span class=\"h5-fs\">Dream venues</span>"
msgstr "<span class=\"h5-fs\">Dream venues</span>"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_media_list
msgid "<span style=\"font-size: 36px;\">Engagement ceremonies</span>"
msgstr "<span style=\"font-size: 36px;\">Engagement ceremonies</span>"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_media_list
msgid "<span style=\"font-size: 36px;\">Vow renewals</span>"
msgstr "<span style=\"font-size: 36px;\">Vow renewals</span>"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_media_list
msgid "<span style=\"font-size: 36px;\">Wedding Receptions</span>"
msgstr "<span style=\"font-size: 36px;\">Wedding Receptions</span>"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_image_title
msgid "A Deep Dive into Romance and Excellence"
msgstr "A Deep Dive into Romance and Excellence"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_sidegrid
msgid ""
"A lovely wedding is a joyful celebration of love, marked by touching vows, "
"beautiful decor, and happy faces, creating unforgettable memories."
msgstr ""
"A lovely wedding is a joyful celebration of love, marked by touching vows, "
"beautiful decor, and happy faces, creating unforgettable memories."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_features_wall
msgid ""
"Access our network of trusted vendors and venues to find the perfect "
"partners for your wedding needs."
msgstr ""
"Access our network of trusted vendors and venues to find the perfect "
"partners for your wedding needs."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_three_columns
msgid "Accomodations"
msgstr "Accomodations"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_detail
msgid ""
"Alexander captures the essence of every moment, creating timeless memories "
"through his lens."
msgstr ""
"Alexander captures the essence of every moment, creating timeless memories "
"through his lens."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_basic
msgid "Aline Turner"
msgstr "Aline Turner"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_media_list
msgid ""
"As a full service experience, we monitor all aspects of your wedding from "
"planning to execution."
msgstr ""
"As a full service experience, we monitor all aspects of your wedding from "
"planning to execution."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_masonry_block_default_template
msgid "Attire"
msgstr "Attire"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_carousel_intro
msgid "Begin your love story with us"
msgstr "Begin your love story with us"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cards_grid
msgid "Bespoke Wedding Planning"
msgstr "Bespoke Wedding Planning"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_key_benefits
msgid "Bespoke Wedding Plans"
msgstr "Bespoke Wedding Plans"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid ""
"Bespoke floral arrangements designed to match your wedding theme, including "
"bridal bouquets, centerpieces, and ceremony flowers."
msgstr ""
"Bespoke floral arrangements designed to match your wedding theme, including "
"bridal bouquets, centerpieces, and ceremony flowers."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cta_box
msgid "Book now"
msgstr "Đặt ngay"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_striped_center_top
msgid "Capture Your Love Story Beautifully"
msgstr "Capture Your Love Story Beautifully"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quadrant
msgid ""
"Capture the magic of your special day with our professional wedding "
"photography services. We help you create memories that last a "
"lifetime.<br/><br/> Let us tell your love story."
msgstr ""
"Capture the magic of your special day with our professional wedding "
"photography services. We help you create memories that last a "
"lifetime.<br/><br/> Let us tell your love story."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_card_offset
msgid "Capturing Your Love Story"
msgstr "Capturing Your Love Story"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_three_columns
msgid "Car Rental"
msgstr "Car Rental"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_masonry_block_default_template
msgid "Caterers"
msgstr "Caterers"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_unveil
msgid "Celebrate love with unforgettable moments and exquisite details."
msgstr "Celebrate love with unforgettable moments and exquisite details."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_masonry_block_default_template
msgid "Check out our carefully curated list of favorite venues."
msgstr "Check out our carefully curated list of favorite venues."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_masonry_block_default_template
msgid "Check out our list of favorite caterers."
msgstr "Check out our list of favorite caterers."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_card_offset
msgid "Cherish every moment."
msgstr "Cherish every moment."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_basic
msgid "Clair Stark"
msgstr "Clair Stark"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team
msgid ""
"Clair has a background in both design and photography. With these strong "
"skills in his toolbox, he will take care of the decoration of the place, as "
"well as create lifelong memories for you and your soulmate."
msgstr ""
"Clair has a background in both design and photography. With these strong "
"skills in his toolbox, he will take care of the decoration of the place, as "
"well as create lifelong memories for you and your soulmate."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid ""
"Comprehensive wedding planning service including timeline creation, vendor "
"coordination, and on-the-day management to ensure a seamless event."
msgstr ""
"Comprehensive wedding planning service including timeline creation, vendor "
"coordination, and on-the-day management to ensure a seamless event."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_call_to_action
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_intro_pill
msgid "Contact us"
msgstr "Liên hệ"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_carousel_intro
msgid "Crafting your perfect wedding day"
msgstr "Crafting your perfect wedding day"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_key_images
msgid "Create Your Perfect Day with Us"
msgstr "Create Your Perfect Day with Us"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_carousel_intro
msgid ""
"Create an unforgettable wedding with our expert planning services that blend"
" elegance, personal touch, and seamless execution."
msgstr ""
"Create an unforgettable wedding with our expert planning services that blend"
" elegance, personal touch, and seamless execution."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cta_box
msgid ""
"Create timeproof memories of this unique moment with our wedding photography"
" services<br/><br/>"
msgstr ""
"Create timeproof memories of this unique moment with our wedding photography"
" services<br/><br/>"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_features_wall
msgid ""
"Create your dream wedding with customized design services that reflect your "
"unique style and preferences."
msgstr ""
"Create your dream wedding with customized design services that reflect your "
"unique style and preferences."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_framed_intro
msgid "Creating Unforgettable Moments: Your Dream Wedding Awaits"
msgstr "Creating Unforgettable Moments: Your Dream Wedding Awaits"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_kickoff
msgid "Creating the perfect day<br/>just as you imagined"
msgstr "Creating the perfect day<br/>just as you imagined"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_empowerment
msgid "Creating unforgettable moments<br/>with expert wedding planning"
msgstr "Creating unforgettable moments<br/>with expert wedding planning"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "Custom Floral Arrangements"
msgstr "Custom Floral Arrangements"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_key_images
msgid "Customizable packages to suit every budget and preference"
msgstr "Customizable packages to suit every budget and preference"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid ""
"Customized lighting solutions and ambiance settings to enhance the mood and "
"atmosphere of your wedding venue, including fairy lights and chandeliers."
msgstr ""
"Customized lighting solutions and ambiance settings to enhance the mood and "
"atmosphere of your wedding venue, including fairy lights and chandeliers."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cards_grid
msgid "Dance and Music Entertainment"
msgstr "Dance and Music Entertainment"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_detail
msgid ""
"Daniel is a culinary artist who crafts exquisite menus tailored to each "
"couple's taste."
msgstr ""
"Daniel is a culinary artist who crafts exquisite menus tailored to each "
"couple's taste."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "Day-Of Coordination"
msgstr "Day-Of Coordination"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_key_images
msgid "Designing Unforgettable Weddings Tailored to Your Dreams"
msgstr "Designing Unforgettable Weddings Tailored to Your Dreams"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_images_mosaic
msgid "Discover Our Wedding Packages"
msgstr "Discover Our Wedding Packages"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cards_grid
msgid ""
"Discover breathtaking venues that capture the essence of romance, from grand"
" castles to scenic beach resorts, perfect for your unforgettable day."
msgstr ""
"Discover breathtaking venues that capture the essence of romance, from grand"
" castles to scenic beach resorts, perfect for your unforgettable day."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_call_to_action
msgid "Discover how we can help you<br/>making this day unforgettable."
msgstr "Discover how we can help you<br/>making this day unforgettable."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_freegrid
msgid "Discover some of the latest<br/>moments we helped to create"
msgstr "Discover some of the latest<br/>moments we helped to create"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cards_grid
msgid "Dream Wedding Venues"
msgstr "Dream Wedding Venues"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "Elegant Venue Decoration"
msgstr "Elegant Venue Decoration"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_striped_top
msgid "Elegant Wedding Planning for Your Perfect Day"
msgstr "Elegant Wedding Planning for Your Perfect Day"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_key_images
msgid "Elegant venues and bespoke decor to match your style"
msgstr "Elegant venues and bespoke decor to match your style"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cards_grid
msgid ""
"Embark on a romantic getaway with our exclusive honeymoon packages, "
"featuring the world’s most enchanting destinations and experiences."
msgstr ""
"Embark on a romantic getaway with our exclusive honeymoon packages, "
"featuring the world’s most enchanting destinations and experiences."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_detail
msgid ""
"Emily is a creative florist who brings vibrant floral designs to life, "
"enhancing every event."
msgstr ""
"Emily is a creative florist who brings vibrant floral designs to life, "
"enhancing every event."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_carousel_intro
msgid ""
"Enhance your celebration with our bespoke solutions and professional "
"guidance."
msgstr ""
"Enhance your celebration with our bespoke solutions and professional "
"guidance."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_features_wall
msgid ""
"Enjoy a stress-free planning process with our comprehensive coordination "
"services, managing every detail from start to finish."
msgstr ""
"Enjoy a stress-free planning process with our comprehensive coordination "
"services, managing every detail from start to finish."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_image_hexagonal
msgid "Enjoy the moment, we take care of everything"
msgstr "Enjoy the moment, we take care of everything"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quotes_carousel
msgid "Everything was so well-coordinated, we couldn't have asked for more!"
msgstr "Everything was so well-coordinated, we couldn't have asked for more!"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_key_benefits
msgid "Exclusive Vendor Access"
msgstr "Exclusive Vendor Access"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_sidegrid
msgid "Experience a lovely wedding experience"
msgstr "Experience a lovely wedding experience"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_carousel_intro
msgid ""
"Explore more and find wedding services that align with your style, crafted "
"to make your special day truly magical and memorable."
msgstr ""
"Explore more and find wedding services that align with your style, crafted "
"to make your special day truly magical and memorable."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid ""
"Explore our exclusive wedding services designed to create unforgettable "
"moments. From planning to execution, we ensure every detail is perfect for "
"your special day."
msgstr ""
"Explore our exclusive wedding services designed to create unforgettable "
"moments. From planning to execution, we ensure every detail is perfect for "
"your special day."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_images_mosaic
msgid "Explore our new offerings to create your dream wedding day."
msgstr "Explore our new offerings to create your dream wedding day."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_masonry_block_default_template
msgid "FAQ"
msgstr "Câu hỏi thường gặp"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_masonry_block_default_template
msgid "Find a Caterer"
msgstr "Find a Caterer"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_text_image
msgid "Find a photographer"
msgstr "Find a photographer"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_image_text
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_masonry_block_default_template
msgid "Find a venue"
msgstr "Find a venue"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_masonry_block_default_template
msgid "Find the answers to all your questions in our FAQ."
msgstr "Find the answers to all your questions in our FAQ."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_masonry_block_default_template
msgid "Find the perfect outfit for before, during and after the main event."
msgstr "Find the perfect outfit for before, during and after the main event."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_basic
msgid "Founder &amp; Planner"
msgstr "Founder &amp; Planner"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_features
msgid ""
"From photography to videography, we will record all the important moments."
msgstr ""
"From photography to videography, we will record all the important moments."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cards_grid
msgid ""
"From romantic first dances to lively late-night parties, we'll create the "
"perfect mood on a stunning dancefloor for your special day."
msgstr ""
"From romantic first dances to lively late-night parties, we'll create the "
"perfect mood on a stunning dancefloor for your special day."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "Full Wedding Planning Package"
msgstr "Full Wedding Planning Package"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_features_wall
msgid "Full-Service Coordination"
msgstr "Full-Service Coordination"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_key_benefits
msgid "Full-Service Support"
msgstr "Full-Service Support"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_key_benefits
msgid ""
"Gain access to a curated list of top-tier vendors and unique venues, "
"offering exclusive options that enhance the elegance and individuality of "
"your wedding."
msgstr ""
"Gain access to a curated list of top-tier vendors and unique venues, "
"offering exclusive options that enhance the elegance and individuality of "
"your wedding."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_kickoff
msgid "Get Started"
msgstr "Get Started"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_empowerment
msgid "Get a quote"
msgstr "Get a quote"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_text_cover
msgid "Get started"
msgstr "Bắt đầu"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_masonry_block_default_template
msgid "Go to FAQ"
msgstr "Go to FAQ"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_picture
msgid ""
"Intimate or with hundreds of guests, outdoors or indoors : we organize the "
"wedding of your dreams."
msgstr ""
"Intimate or with hundreds of guests, outdoors or indoors : we organize the "
"wedding of your dreams."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_basic
msgid "Iris Joe"
msgstr "Iris Joe"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quotes_carousel
msgid "Jack and Mary"
msgstr "Jack and Mary"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_detail
msgid ""
"James is a visionary wedding coordinator and stylist with a passion for "
"transforming dreams into reality."
msgstr ""
"James is a visionary wedding coordinator and stylist with a passion for "
"transforming dreams into reality."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_banner
msgid "Just as you dreamt it."
msgstr "Just as you dreamt it."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_striped_center_top
msgid ""
"Let us help you capture the magic of your special day with stunning "
"photography and personalized wedding services."
msgstr ""
"Let us help you capture the magic of your special day with stunning "
"photography and personalized wedding services."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_card_offset
msgid ""
"Let us help you capture the magic of your special day. Our professional "
"photography services ensure that your wedding memories are preserved "
"beautifully for a lifetime."
msgstr ""
"Let us help you capture the magic of your special day. Our professional "
"photography services ensure that your wedding memories are preserved "
"beautifully for a lifetime."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_shape_image
msgid ""
"Let us make your wedding day unforgettable with our expert planning "
"services. From venue selection to coordination on the big day, we handle "
"every detail to create a seamless and personalized celebration that reflects"
" your unique vision."
msgstr ""
"Let us make your wedding day unforgettable with our expert planning "
"services. From venue selection to coordination on the big day, we handle "
"every detail to create a seamless and personalized celebration that reflects"
" your unique vision."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_striped_top
msgid "Let us plan your perfect day"
msgstr "Let us plan your perfect day"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_key_images
msgid "Let us turn your vision into a beautiful reality"
msgstr "Let us turn your vision into a beautiful reality"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "Lighting &amp; Ambiance"
msgstr "Lighting &amp; Ambiance"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quotes_carousel
msgid "Linda and Paul"
msgstr "Linda and Paul"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team
msgid "Livia Sailor"
msgstr "Livia Sailor"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team
msgid ""
"Livia is our head wedding coordinator and stylist, she'll turn any of your "
"ideas into reality, making sure your wedding day is smooth and looks better "
"than anything you have ever seen on Pinterest."
msgstr ""
"Livia is our head wedding coordinator and stylist, she'll turn any of your "
"ideas into reality, making sure your wedding day is smooth and looks better "
"than anything you have ever seen on Pinterest."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_picture
msgid "Love is in the air"
msgstr "Love is in the air"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cards_grid
msgid "Luxury Honeymoon Packages"
msgstr "Luxury Honeymoon Packages"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_three_columns
msgid "Make up &amp;amp; Hair"
msgstr "Make up &amp;amp; Hair"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quotes_carousel
msgid "Married in 2019"
msgstr "Married in 2019"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quotes_carousel
msgid "Married in 2020"
msgstr "Married in 2020"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_basic
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_title
msgid "Meet our team"
msgstr "Meet our team"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cover
msgid "Meet us"
msgstr "Meet us"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_striped_center_top
msgid "Moments crafted with love"
msgstr "Moments crafted with love"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_image_text_overlap
msgid "OUR MISSION"
msgstr "OUR MISSION"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_detail
msgid ""
"Olivia is an expert coordinator who ensures every detail is perfectly "
"executed on the big day."
msgstr ""
"Olivia is an expert coordinator who ensures every detail is perfectly "
"executed on the big day."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid ""
"On-the-day coordination to oversee and manage the wedding activities, "
"ensuring everything runs smoothly according to the planned schedule."
msgstr ""
"On-the-day coordination to oversee and manage the wedding activities, "
"ensuring everything runs smoothly according to the planned schedule."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cover
msgid "Once in a lifetime moments"
msgstr "Once in a lifetime moments"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "Our Wedding Services"
msgstr "Our Wedding Services"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cards_grid
msgid ""
"Our expert team crafts personalized wedding experiences, managing every "
"detail so you can enjoy a stress-free celebration with your loved ones."
msgstr ""
"Our expert team crafts personalized wedding experiences, managing every "
"detail so you can enjoy a stress-free celebration with your loved ones."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_image_text_overlap
msgid ""
"Our goal is to ensure this day will be unforgettable for you and your "
"beloved soulmate."
msgstr ""
"Our goal is to ensure this day will be unforgettable for you and your "
"beloved soulmate."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_empowerment
msgid "Our services"
msgstr "Dịch vụ của chúng tôi"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_key_benefits
msgid ""
"Our team provides comprehensive support from initial consultation to the big"
" day, handling every aspect to ensure a smooth and stress-free experience."
msgstr ""
"Our team provides comprehensive support from initial consultation to the big"
" day, handling every aspect to ensure a smooth and stress-free experience."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_freegrid
msgid "PORTFOLIO"
msgstr "PORTFOLIO"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "Partial Wedding Planning"
msgstr "Partial Wedding Planning"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_features_wall
msgid "Personalized Wedding Design"
msgstr "Personalized Wedding Design"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_text_cover
msgid "Plan Weddings.<br/>Effortlessly."
msgstr "Plan Weddings.<br/>Effortlessly."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_intro_pill
msgid "Plan your<br/>dream wedding"
msgstr "Plan your<br/>dream wedding"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_basic
msgid "Professional Photographer"
msgstr "Professional Photographer"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_key_images
msgid "Professional planning and coordination for a seamless event"
msgstr "Professional planning and coordination for a seamless event"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quotes_carousel
msgid "Renewed their vows in 2021"
msgstr "Renewed their vows in 2021"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quotes_carousel
msgid "Rose and Peter"
msgstr "Rose and Peter"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_media_list
msgid "See how others renewed their vows"
msgstr "See how others renewed their vows"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_empowerment
msgid "See our wedding galleries   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr ""
"See our wedding galleries   <i class=\"fa fa-long-arrow-right\" "
"role=\"img\"/>"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_masonry_block_default_template
msgid "Shop"
msgstr "Cửa hàng"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_detail
msgid ""
"Sophia is a talented designer known for her innovative approach in creating "
"stunning wedding experiences."
msgstr ""
"Sophia is a talented designer known for her innovative approach in creating "
"stunning wedding experiences."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid ""
"Sophisticated venue decoration service including floral arrangements, table "
"settings, and thematic decor to create a stunning atmosphere for your "
"wedding."
msgstr ""
"Sophisticated venue decoration service including floral arrangements, table "
"settings, and thematic decor to create a stunning atmosphere for your "
"wedding."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_media_list
msgid "Start planning your engagement"
msgstr "Bắt đầu lên kế hoạch cho lễ đính hôn của bạn"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_media_list
msgid "Start planning your wedding"
msgstr "Bắt đầu lên kế hoạch cho đám cưới của bạn"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_picture
msgid "Stuart and Iliana's wedding at the Barn."
msgstr "Đám cưới của Stuart và Iliana tại Barn."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid ""
"Tailored planning assistance for specific aspects of your wedding, including"
" vendor selection and coordination of key elements of your day."
msgstr ""
"Tailored planning assistance for specific aspects of your wedding, including"
" vendor selection and coordination of key elements of your day."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_carousel_intro
msgid "Tailored wedding planning"
msgstr "Tailored wedding planning"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_detail
msgid "The Caterer"
msgstr "The Caterer"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_detail
msgid "The Coordinator"
msgstr "The Coordinator"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_basic
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_detail
msgid "The Designer"
msgstr "The Designer"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_detail
msgid "The Florist"
msgstr "The Florist"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_detail
msgid "The Founder &amp; Planner"
msgstr "The Founder &amp; Planner"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_detail
msgid "The Photographer"
msgstr "The Photographer"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_basic
msgid "The Stylist"
msgstr "The Stylist"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_media_list
msgid ""
"The heartfelt exchange of wedding vows is often the most memorable part of a"
" marriage ceremony."
msgstr ""
"The heartfelt exchange of wedding vows is often the most memorable part of a"
" marriage ceremony."

#. module: theme_yes
#: model:ir.model,name:theme_yes.model_theme_utils
msgid "Theme Utils"
msgstr "Chủ đề Utils"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quotes_carousel
msgid "They made our big day stree-free and absolutely beautiful!"
msgstr "They made our big day stree-free and absolutely beautiful!"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quotes_carousel
msgid "They turned our dream into reality with ease and professionalism!"
msgstr "They turned our dream into reality with ease and professionalism!"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_company_team_basic
msgid "Tony Fred"
msgstr "Tony Fred"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_image_title
msgid ""
"Transform your special day with our bespoke wedding services, where elegance"
" meets personalized touches. Elevate your celebration with arrangements that"
" blend sophistication and love seamlessly."
msgstr ""
"Transform your special day with our bespoke wedding services, where elegance"
" meets personalized touches. Elevate your celebration with arrangements that"
" blend sophistication and love seamlessly."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cta_box
msgid "Treat yourself<br/>with great pictures"
msgstr "Treat yourself<br/>with great pictures"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_features_wall
msgid "Vendor &amp; Venue Selection"
msgstr "Vendor &amp; Venue Selection"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_masonry_block_default_template
msgid "Venues"
msgstr "Địa điểm "

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_striped_center_top
msgid "View Our Portfolio"
msgstr "View Our Portfolio"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quadrant
msgid "View Portfolio"
msgstr "View Portfolio"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_freegrid
msgid "View all the pictures"
msgstr "View all the pictures"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.configurator_s_title
msgid "We are two passionate<br/>with years of experience"
msgstr "We are two passionate<br/>with years of experience"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_key_benefits
msgid ""
"We create personalized wedding plans that reflect your vision and style, "
"ensuring every detail is crafted to perfection for your special day."
msgstr ""
"We create personalized wedding plans that reflect your vision and style, "
"ensuring every detail is crafted to perfection for your special day."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_features
msgid ""
"We help you crafting a personalized wedding menu tailored to your tastes."
msgstr ""
"We help you crafting a personalized wedding menu tailored to your tastes."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_features
msgid ""
"We help you discover the ideal location that matches your style and vision."
msgstr ""
"We help you discover the ideal location that matches your style and vision."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_quadrant
msgid "Wedding Memories"
msgstr "Wedding Memories"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_shape_image
msgid "Wedding planning"
msgstr "Wedding planning"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_media_list
msgid ""
"We’ll help you mark the engagement milestone with an unforgettable ceremony."
msgstr ""
"We’ll help you mark the engagement milestone with an unforgettable ceremony."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_cover
msgid "You focus on the big day, let us focus on you."
msgstr "You focus on the big day, let us focus on you."

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_unveil
msgid "Your perfect day awaits"
msgstr "Your perfect day awaits"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_big_number
msgid "personalized invitations created"
msgstr "personalized invitations created"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "✽  Venue &amp; Decor"
msgstr "✽  Venue &amp; Decor"

#. module: theme_yes
#: model_terms:theme.ir.ui.view,arch:theme_yes.s_pricelist_boxed
msgid "✽  Wedding Planning"
msgstr "✽  Wedding Planning"
