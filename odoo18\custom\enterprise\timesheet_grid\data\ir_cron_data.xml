<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="1">
    <!-- <PERSON><PERSON> Reminder Cron -->
    <record id="timesheet_reminder_user" model="ir.cron" forcecreate="True">
        <field name="name">Timesheet: Employees Email Reminder</field>
        <field name="model_id" ref="base.model_res_company"/>
        <field name="state">code</field>
        <field name="code">model._cron_timesheet_reminder_employee()</field>
        <field name="user_id" ref="base.user_root"/>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="active" eval="True"/>
    </record>

    <record id="timesheet_reminder" model="ir.cron" forcecreate="True">
        <field name="name">Timesheet: Approvers Email Reminder</field>
        <field name="model_id" ref="base.model_res_company"/>
        <field name="state">code</field>
        <field name="code">model._cron_timesheet_reminder()</field>
        <field name="user_id" ref="base.user_root"/>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="active" eval="True"/>
    </record>
</data></odoo>
