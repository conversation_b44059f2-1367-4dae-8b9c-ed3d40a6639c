//------------------------------------------------------------------------------//
// TODO ideally this should be done in primary variables if it was possible.
//------------------------------------------------------------------------------//

// Headings
@if nth($o-theme-headings-font, 1) == 'Roboto' {
    $o-theme-headings-font-weight:          500;
}
@else if nth($o-theme-headings-font, 1) == 'Nunito' {
    $o-theme-headings-font-weight:          600;
}
@else if nth($o-theme-headings-font, 1) == 'Advent Pro' {
    $o-theme-headings-font-weight:          600;
}

// Body
@if o-website-value('font') == 'Roboto' {
    $o-theme-font-weight-bold:              500;
}
@else if o-website-value('font') == 'Lato' {
    $o-theme-font-weight-light:             300;
    $o-theme-font-weight-bold:              600;
}
@else if o-website-value('font') == 'Advent Pro' {
    $o-theme-font-weight-bold:              600;
}
@else if o-website-value('font') == 'Oswald' {
    $o-theme-font-weight-light:             200;
    $o-theme-font-weight-normal:            300;
    $o-theme-font-weight-bold:              400;
}
@else if o-website-value('font') == 'Open Sans' {
    $o-theme-font-weight-bold:              600;
}
@else if o-website-value('font') == 'Montserrat Alternates' {
    $o-theme-font-weight-bold:              600;
}

// Buttons
@if nth($o-theme-buttons-font, 1) == 'Roboto' {
    $o-theme-btn-font-weight:               500;
}
@else if nth($o-theme-buttons-font, 1) == 'Advent Pro' {
    $o-theme-btn-font-weight:               600;
}
@else if nth($o-theme-buttons-font, 1) == 'Oswald' {
    $o-theme-btn-font-weight:               200;
}
@else if nth($o-theme-buttons-font, 1) == 'Open Sans' {
    $o-theme-btn-font-weight:               600;
}
@else if nth($o-theme-buttons-font, 1) == 'Montserrat Alternates' {
    $o-theme-btn-font-weight:               600;
}

//------------------------------------------------------------------------------//
// Bootstrap
//------------------------------------------------------------------------------//

// Spacing
//
// Control the default styling of most Bootstrap elements by modifying these
// variables. Mostly focused on spacing.
// You can add more entries to the $spacers map, should you need more variation.

$spacer:                                    1.25rem !default;

// Components
//
// Define common padding and border radius sizes and more.

$border-radius:                             .125rem !default;
$border-radius-lg:                          .25rem !default;
$border-radius-sm:                          0 !default;

// Typography
//
// Font, line-height, and color for body text, headings, and more.

$font-size-lg:                              o-website-value('font-size-base') * $o-theme-font-size-lg-multiplier !default;
$font-size-sm:                              o-website-value('font-size-base') * $o-theme-font-size-sm-multiplier !default;

$font-weight-light:                         $o-theme-font-weight-light !default;
$font-weight-normal:                        $o-theme-font-weight-normal !default;
$font-weight-bold:                          $o-theme-font-weight-bold !default;

$headings-font-weight:                      $o-theme-headings-font-weight !default;

// Buttons + Forms
//
// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.

$input-btn-focus-width:                     0 !default;
$input-focus-border-color:                  o-color('primary') !default;

$btn-font-weight:                           $o-theme-btn-font-weight !default;
$btn-box-shadow:                            none !default;

$input-color:                               o-color('o-cc1-text') !default;
$input-placeholder-color:                   o-color('500') !default;

// Cards

$card-spacer-y:                             1rem !default;
$card-spacer-x:                             1.75rem !default;
$card-border-width:                         0 !default;
$card-border-radius:                        $border-radius-lg !default;
