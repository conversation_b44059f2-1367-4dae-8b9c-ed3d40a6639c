<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="timesheet_grid.TimesheetTimerListRenderer" t-inherit="web.ListRenderer" t-inherit-mode="primary">
        <xpath expr="//div[hasclass('o_list_renderer')]" position="before">
            <div t-if="timesheetTimerRendererHook.showTimer" class="pinned_header d-flex flex-wrap justify-content-end align-items-center bg-view border-bottom">
                <div class="flex-grow-1" t-key="timesheetTimerRendererHook.timerState.timesheetId">
                    <TimesheetTimerHeader
                        timesheet="timesheetTimerRendererHook.timesheet"
                        addTimeMode="timesheetTimerRendererHook.timerState.addTimeMode"
                        stepTimer="timesheetTimerRendererHook.timerState.stepTimer"
                        timerRunning="timesheetTimerRendererHook.timerState.timerRunning"
                        otherCompany="timesheetTimerRendererHook.timerState.otherCompany"
                        fields="timesheetTimerRendererHook.fields"
                        onTimerStarted="() => this.timesheetTimerRendererHook._onTimerStarted()"
                        onTimerStopped="() => this.timesheetTimerRendererHook._onTimerStopped()"
                        onTimerUnlinked="() => this.timesheetTimerRendererHook._onTimerUnlinked()"
                        onClick="() => this.timesheetTimerRendererHook.onTimerHeaderClick()"
                        className="'h-100'"
                    />
                </div>
            </div>
        </xpath>
    </t>

</templates>
