# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * timesheet_grid
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2017-10-24 09:24+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Dutch (Belgium) (https://www.transifex.com/odoo/teams/41243/nl_BE/)\n"
"Language: nl_BE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "%s Spent"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_kanban
msgid "<i class=\"fa fa-pause text-warning\" title=\"Timer is Paused\"/>"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_kanban
msgid "<i class=\"fa fa-play text-success\" title=\"Timer is Running\"/>"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid "<span class=\"col-auto ms-2\">min</span>"
msgstr ""

#. module: timesheet_grid
#: model:mail.template,body_html:timesheet_grid.mail_template_timesheet_reminder_user
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Timesheets</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Abigail Peterson</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <t t-set=\"timesheet_hours\" t-value=\"ctx.get('timesheet_hours', 0)\"></t>\n"
"                    <t t-set=\"working_hours\" t-value=\"ctx.get('working_hours', 0)\"></t>\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or ''\">Abigail Peterson</t>,<br><br>\n"
"                        This is a friendly reminder to log your hours for the following period: <t t-out=\"ctx.get('date_start') or ''\">05/05/2021</t> <i class=\"fa fa-long-arrow-right\"></i> <t t-out=\"ctx.get('date_stop') or ''\">05/06/2021</t>.\n"
"                        For the time being, you <t t-if=\"timesheet_hours != 0\">only</t> logged <t t-out=\"'%d' %int(timesheet_hours)\">2</t><t t-if=\"timesheet_hours % 1 != 0\" t-out=\"':%02d' % (round(timesheet_hours % 1 * 60))\">:30</t> hours on the <t t-out=\"'%d' %int(working_hours)\">8</t><t t-if=\"working_hours % 1 != 0\" t-out=\"':%02d' % (round(working_hours % 1 * 60))\">:30</t> requested.<br>\n"
"                        <div t-if=\"ctx.get('action_url')\" style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"ctx.get('action_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 13px;\">Fill in your timesheet</a>\n"
"                        </div>\n"
"                        <br>Thank you,<br>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: timesheet_grid
#: model:mail.template,body_html:timesheet_grid.mail_template_timesheet_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Timesheets</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Abigail Peterson</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or ''\">Abigail Peterson</t>,<br><br>\n"
"                        This is a friendly reminder to approve your team's timesheets for the following period: <t t-out=\"ctx.get('date_start') or ''\">05/05/2021</t> - <t t-out=\"ctx.get('date_stop') or ''\">06/05/2021</t>.<br>\n"
"                        <div t-if=\"ctx.get('action_url')\" style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"ctx.get('action_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 13px;\">Validate the Timesheets</a>\n"
"                        </div>\n"
"                        <br>Thank you,<br>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "A timer is running in another company."
msgstr ""

#. module: timesheet_grid
#: model:ir.ui.menu,name:timesheet_grid.menu_timesheet_grid_validate_all_timesheets
msgid "All Timesheets"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__amount
msgid "Amount"
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "An employee must be linked to your user to record time."
msgstr ""

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_account_analytic_line
msgid "Analytic Account"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_company__timesheet_mail_allow
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__reminder_allow
msgid "Approver Reminder"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_company__timesheet_mail_delay
msgid "Approver Reminder Days"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_company__timesheet_mail_interval
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__reminder_interval
msgid "Approver Reminder Frequency"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.actions.act_window,help:timesheet_grid.timesheet_grid_to_validate_action
msgid "Check that your employees correctly filled in their timesheets and that their time is billable."
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "Click on"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid "Click on the cell to set the number of hours you spent on this project."
msgstr ""

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: timesheet_grid
#: model:mail.template,description:timesheet_grid.mail_template_timesheet_reminder
msgid "Configure reminders in timesheet settings to remind approvers to validate timesheets"
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/task.py:0
msgid "Confirm Time Spent"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid "Congratulations, you are now a master of Timesheets."
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.actions.act_window,help:timesheet_grid.action_timesheet_previous_month
#: model_terms:ir.actions.act_window,help:timesheet_grid.action_timesheet_previous_week
msgid ""
"Congratulations, you are up to date. <br>\n"
"                    Let's wait for your employees to start new activities."
msgstr ""

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_project_task_create_timesheet
msgid "Create Timesheet from task"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__create_uid
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__create_date
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/views/timer_timesheet_grid/timer_timesheet_grid_renderer.xml:0
msgid "Daily overtime"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__date
msgid "Date"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_employee
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_project
msgid "Day"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_gantt_timesheet
msgid "Days Spent —"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__reminder_delay
msgid "Days to Remind Approver"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__reminder_user_delay
msgid "Days to Remind User"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_account_analytic_line__employee_id
msgid "Define an 'hourly cost' on the employee to track the cost of their time."
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_create_timesheet_view_form
msgid "Delete"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_merge_wizard_view_form
msgid "Describe your activity"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid "Describe your activity <i>(e.g. sent an e-mail, meeting with the customer...)</i>."
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/grid_timesheet_timer_header/grid_timesheet_timer_header.js:0
#: code:addons/timesheet_grid/static/src/hooks/timesheet_timer_hooks.js:0
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_create_timesheet_view_form
msgid "Describe your activity..."
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__name
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__description
msgid "Description"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_overtime_indication/timesheet_overtime_indication.js:0
msgid "Difference between the number of %s allocated to the project and the number of %s recorded"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_overtime_indication/timesheet_overtime_indication.js:0
msgid "Difference between the number of %s allocated to the task and the number of %s recorded"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/employee_overtime_indication/employee_overtime_indication.xml:0
msgid "Difference between the number of hours recorded and the number of hours the employee was supposed to work according to his contract"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_overtime_indication/timesheet_overtime_indication.js:0
msgid "Difference between the time allocated and the time recorded"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_create_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_merge_wizard_view_form
msgid "Discard"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__display_name
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__display_timer
msgid "Display Timer"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__display_timer_pause
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task__display_timer_pause
msgid "Display Timer Pause"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__display_timer_resume
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task__display_timer_resume
msgid "Display Timer Resume"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__display_timer_start_primary
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task__display_timer_start_primary
msgid "Display Timer Start Primary"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task__display_timer_start_secondary
msgid "Display Timer Start Secondary"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__display_timer_stop
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task__display_timer_stop
msgid "Display Timer Stop"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task__display_timesheet_timer
msgid "Display Timesheet Time"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields.selection,name:timesheet_grid.selection__account_analytic_line__validated_status__draft
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_search
msgid "Draft"
msgstr ""

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_hr_employee
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__employee_id
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__employee_id
msgid "Employee"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_company__timesheet_mail_employee_interval
msgid "Employee Frequency"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_company__timesheet_mail_employee_allow
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__reminder_user_allow
msgid "Employee Reminder"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_company__timesheet_mail_employee_delay
msgid "Employee Reminder Days"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__encoding_uom_id
msgid "Encoding Uom"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "Enter"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid "Frequency"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/views/timesheet_grid/timesheet_grid_search_model.js:0
msgid "Grouping by date is not supported"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_create_timesheet_view_form
msgid "Hours Spent"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_gantt_timesheet
msgid "Hours Spent —"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__id
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__id
msgid "ID"
msgstr "ID"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__is_timer_running
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task__is_timer_running
#: model:ir.model.fields,field_description:timesheet_grid.field_timesheets_analysis_report__is_timer_running
msgid "Is Timer Running"
msgstr ""

#. module: timesheet_grid
#: model:ir.ui.menu,name:timesheet_grid.menu_timesheet_grid_validate_previous_month
msgid "Last Month"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__write_uid
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__write_date
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_employee__last_validated_timesheet_date
msgid "Last Validated Timesheet Date"
msgstr ""

#. module: timesheet_grid
#: model:ir.ui.menu,name:timesheet_grid.menu_timesheet_grid_validate_previous_week
msgid "Last Week"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid "Launch the <b>timer</b> for this project by pressing the <b>[a] key</b>. Easily switch from one project to another by using those keys. <i>Tip: you can also directly add 15 minutes to this project by hitting the <b>shift + [A] keys</b>.</i>"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid "Launch the <b>timer</b> to start a new activity."
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_merge_wizard_view_form
msgid "Merge"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_merge_wizard_view_form
msgid "Merge Timesheet"
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
#: model:ir.actions.server,name:timesheet_grid.merge_timesheet_action
#: model:ir.model,name:timesheet_grid.model_hr_timesheet_merge_wizard
msgid "Merge Timesheets"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__timesheet_min_duration
msgid "Minimal Duration"
msgstr ""

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_ir_module_module
msgid "Module"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_employee
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_project
msgid "Month"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_search
msgid "My Department"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_search
msgid "My Projects"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_search
msgid "My Tasks"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_search
msgid "My Team"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_company__timesheet_mail_nextdate
msgid "Next scheduled date for approver reminder"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_company__timesheet_mail_employee_nextdate
msgid "Next scheduled date for employee reminder"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.actions.act_window,help:timesheet_grid.action_timesheet_previous_month
#: model_terms:ir.actions.act_window,help:timesheet_grid.action_timesheet_previous_week
msgid "No activities to validate."
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.actions.act_window,help:timesheet_grid.timesheet_grid_to_validate_action
msgid "No timesheets to validate"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_res_config_settings__reminder_delay
msgid "Number of days after the end of the week/month after which an automatic email reminder will be sent to timesheet managers that still have timesheets to validate."
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_res_config_settings__reminder_user_delay
msgid "Numbers of days after the end of the week/month after which an automatic email reminder will be sent to timesheet users that still have timesheets to encode (according to their working hours)."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "Only a Timesheets Approver or Manager is allowed to modify a validated entry."
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_form
msgid "Pause"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "Press"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "Press Esc to discard"
msgstr ""

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_project_project
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__project_id
msgid "Project"
msgstr ""

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_hr_employee_public
msgid "Public Employee"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__unit_amount
msgid "Quantity"
msgstr ""

#. module: timesheet_grid
#: model:mail.template,subject:timesheet_grid.mail_template_timesheet_reminder_user
msgid "Reminder to log your timesheets"
msgstr ""

#. module: timesheet_grid
#: model:mail.template,subject:timesheet_grid.mail_template_timesheet_reminder
msgid "Reminder to validate your team's timesheets"
msgstr ""

#. module: timesheet_grid
#: model:ir.actions.server,name:timesheet_grid.invalidate_timesheet_action
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_form_view
msgid "Reset to draft"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_form
msgid "Resume"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__timesheet_rounding
msgid "Round up"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid "Rounding applied when tracking your time using the timer"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_create_timesheet_view_form
msgid "Save"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_create_timesheet_view_form
msgid "Save time"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid "Select the <b>project</b> on which you are working."
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_hr_employee__timesheet_manager_id
#: model:ir.model.fields,help:timesheet_grid.field_res_users__timesheet_manager_id
msgid ""
"Select the user responsible for approving \"Timesheet\" of this employee.\n"
"If empty, the approval is done by a Timesheets > Administrator or a Timesheets > User: all timesheets (as determined in the users settings)."
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_account_analytic_line__is_timesheet
msgid "Set if this analytic line represents a line of timesheet."
msgstr ""

#. module: timesheet_grid
#: model:mail.template,description:timesheet_grid.mail_template_timesheet_reminder_user
msgid "Set reminders in settings to notify employees who didn't record their timesheet"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid "Set the number of hours you spent on this project (e.g. 1:30 or 1.5). <i>Tip: use the tab keys to easily navigate from one cell to another.</i>"
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "Sorry, you cannot use a timer for a validated timesheet"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
#: code:addons/timesheet_grid/static/src/components/timesheet_uom_hour_timer/timesheet_uom_hour_timer.js:0
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_form
msgid "Start"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
#: code:addons/timesheet_grid/static/src/components/timesheet_uom_hour_timer/timesheet_uom_hour_timer.js:0
#: model_terms:ir.ui.view,arch_db:timesheet_grid.project_task_view_form
msgid "Stop"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid "Stop the <b>timer</b> when you are done. <i>Tip: hit <b>[Enter]</b> in the description to automatically log your activity.</i>"
msgstr ""

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_project_task
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__task_id
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__task_id
msgid "Task"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_project_task_create_timesheet__task_id
msgid "Task for which we are creating a sales order"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_account_analytic_line__display_timer
msgid "Technical field used to display the timer if the encoding unit is 'Hours'."
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid "The duration of timesheets encoded through the timer will automatically be rounded up to this number. For instance, if you stop your timer at 00:16:56, the duration of the timesheet entry will automatically be rounded up to 00:30 (assuming you have a round up of 15 min). We recommend having the same value for the minimal duration and for the round up."
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/views/timesheet_grid/timesheet_grid_controller.js:0
msgid "The timesheet entry has successfully been created."
msgstr ""

#. module: timesheet_grid
#: model:ir.model.constraint,message:timesheet_grid.constraint_project_task_create_timesheet_time_positive
msgid "The timesheet's time must be positive"
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/wizard/timesheet_merge_wizard.py:0
msgid "The timesheets have successfully been merged."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "The timesheets have successfully been reset to draft."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "The timesheets have successfully been validated."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/wizard/timesheet_merge_wizard.py:0
msgid "The timesheets must have the same encoding unit"
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "There are no timesheets to merge."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "There are no timesheets to reset to draft or they have already been invoiced."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/task.py:0
msgid "This project isn't expected to have task during this period. Planned hours :"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task_create_timesheet__time_spent
msgid "Time"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid "Time Rounding"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_merge_wizard_view_form
msgid "Time Spent"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__timer_pause
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task__timer_pause
msgid "Timer Last Pause"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__timer_start
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task__timer_start
msgid "Timer Start"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_employee__timesheet_manager_id
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_employee_public__timesheet_manager_id
#: model:ir.model.fields,field_description:timesheet_grid.field_res_users__timesheet_manager_id
msgid "Timesheet"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.view_employee_tree_inherit_timesheet
msgid "Timesheet Approver"
msgstr ""

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_timesheet_grid_mixin
msgid "Timesheet Grid mixin"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__duration_unit_amount
msgid "Timesheet Init Amount"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__is_timesheet
#: model:ir.model.fields,field_description:timesheet_grid.field_timesheets_analysis_report__is_timesheet
msgid "Timesheet Line"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__unit_amount_validate
msgid "Timesheet Unit Time"
msgstr ""

#. module: timesheet_grid
#: model:ir.actions.server,name:timesheet_grid.timesheet_reminder_ir_actions_server
msgid "Timesheet: Approvers Email Reminder"
msgstr ""

#. module: timesheet_grid
#: model:ir.actions.server,name:timesheet_grid.timesheet_reminder_user_ir_actions_server
msgid "Timesheet: Employees Email Reminder"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_hr_timesheet_merge_wizard__timesheet_ids
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_employee
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_project
msgid "Timesheets"
msgstr ""

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "Timesheets before the %s (included) have been validated, and can no longer be %s."
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid "Timesheets encoded via the timer that do not meet the minimal duration will automatically be rounded up to the defined value. For instance, if you stop your timer at 00:04:36, the duration of the timesheet entry will automatically be rounded up to 00:15 (assuming you have a minimal duration of 15 min). We recommend having the same value for the minimal duration and for the round up."
msgstr ""

#. module: timesheet_grid
#: model:ir.actions.act_window,name:timesheet_grid.action_timesheet_previous_month
#: model:ir.actions.act_window,name:timesheet_grid.action_timesheet_previous_week
#: model:ir.actions.act_window,name:timesheet_grid.timesheet_grid_to_validate_action
msgid "Timesheets to Validate"
msgstr ""

#. module: timesheet_grid
#: model:mail.template,name:timesheet_grid.mail_template_timesheet_reminder
msgid "Timesheets: Approver Reminder"
msgstr ""

#. module: timesheet_grid
#: model:mail.template,name:timesheet_grid.mail_template_timesheet_reminder_user
msgid "Timesheets: Employee Reminder"
msgstr ""

#. module: timesheet_grid
#: model:ir.ui.menu,name:timesheet_grid.menu_timesheet_grid_validate
msgid "To Validate"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/tours/timesheet_grid.js:0
msgid "Track the <b>time spent</b> on your projects. <i>It starts here.</i>"
msgstr ""

#. module: timesheet_grid
#: model:ir.model,name:timesheet_grid.model_res_users
msgid "User"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__user_can_validate
msgid "User Can Validate"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_res_config_settings__reminder_user_interval
msgid "User Reminder Frequency"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__user_timer_id
#: model:ir.model.fields,field_description:timesheet_grid.field_project_task__user_timer_id
msgid "User Timer"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_hr_employee_public__timesheet_manager_id
msgid "User responsible of timesheet validation. Should be Timesheet Manager."
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/js/views/timesheet_pivot/timesheet_validation_pivot_controller.xml:0
#: code:addons/timesheet_grid/static/src/views/timesheet_kanban/timesheet_validation_kanban_controller.xml:0
#: model:ir.actions.server,name:timesheet_grid.timesheet_validate_action
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_form_view
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_employee_validation
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_tree_user_inherited
msgid "Validate"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields.selection,name:timesheet_grid.selection__account_analytic_line__validated_status__validated
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_search
msgid "Validated"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__validated_status
msgid "Validated Status"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,field_description:timesheet_grid.field_account_analytic_line__validated
#: model:ir.model.fields,field_description:timesheet_grid.field_timesheets_analysis_report__validated
msgid "Validated line"
msgstr ""

#. module: timesheet_grid
#: model:ir.actions.server,name:timesheet_grid.timesheet_grid_all_timesheets_action_server
msgid "Validation All Timesheet Server Action"
msgstr ""

#. module: timesheet_grid
#: model:ir.actions.server,name:timesheet_grid.timesheet_grid_week_action_server
msgid "Validation Timesheet Week_view Server Action"
msgstr ""

#. module: timesheet_grid
#: model:ir.actions.server,name:timesheet_grid.timesheet_grid_month_action_server
msgid "Validation Timesheet month_view Server Action"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_employee
#: model_terms:ir.ui.view,arch_db:timesheet_grid.timesheet_view_grid_by_project
msgid "Week"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/views/timer_timesheet_grid/timer_timesheet_grid_renderer.xml:0
msgid "Weekly overtime"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields,help:timesheet_grid.field_account_analytic_line__user_can_validate
msgid "Whether or not the current user can validate/reset to draft the record."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "You can only reset to draft the timesheets of employees of whom you are the manager or the timesheet approver."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "You can only validate the timesheets of employees of whom you are the manager or the timesheet approver."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "You cannot access timesheets that are not yours."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "You cannot adjust the time of the timesheet for a project with timesheets disabled."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "You cannot delete a validated entry. Please, contact your manager or your timesheet approver."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/project.py:0
msgid "You cannot start the timer for a project in a company encoding its timesheets in days."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "You cannot use the timer on validated timesheets."
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "You cannot validate the timesheets from employees that are not part of your team or there are no timesheets to validate."
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields.selection,name:timesheet_grid.selection__res_company__timesheet_mail_employee_interval__months
#: model:ir.model.fields.selection,name:timesheet_grid.selection__res_company__timesheet_mail_interval__months
msgid "after the end of the month"
msgstr ""

#. module: timesheet_grid
#: model:ir.model.fields.selection,name:timesheet_grid.selection__res_company__timesheet_mail_employee_interval__weeks
#: model:ir.model.fields.selection,name:timesheet_grid.selection__res_company__timesheet_mail_interval__weeks
msgid "after the end of the week"
msgstr ""

#. module: timesheet_grid
#: model_terms:ir.ui.view,arch_db:timesheet_grid.res_config_settings_view_form
msgid "days"
msgstr "dagen"

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "deleted"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "min"
msgstr ""

#. module: timesheet_grid
#. odoo-python
#: code:addons/timesheet_grid/models/analytic.py:0
msgid "modified"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "or a letter to start the timer"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "to add"
msgstr ""

#. module: timesheet_grid
#. odoo-javascript
#: code:addons/timesheet_grid/static/src/components/timesheet_timer_header/timesheet_timer_header.xml:0
msgid "to start the timer"
msgstr ""
