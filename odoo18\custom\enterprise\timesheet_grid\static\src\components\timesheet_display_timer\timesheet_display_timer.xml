<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="timesheet_grid.TimesheetTimerFloatTimeField" t-inherit="web.FloatTimeField">
        <xpath expr="//span" position="attributes">
            <attribute name="t-att-class">props.timerRunning and props.displayRed ? "fw-bold text-danger" : ""</attribute>
        </xpath>
        <xpath expr="//input" position="attributes">
            <attribute name="t-att-class">props.timerRunning and props.displayRed ? "fw-bold text-danger" : ""</attribute>
        </xpath>
    </t>

    <t t-name="timesheet_grid.TimesheetDisplayTimer">
        <TimesheetTimerFloatTimerField t-props="TimesheetTimerFloatTimerFieldProps"/>
    </t>

</templates>
