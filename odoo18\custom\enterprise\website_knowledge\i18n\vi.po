# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_knowledge
# 
# Translators:
# Wil <PERSON>doo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:16+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "<i class=\"fa fa-lg fa-bars\" title=\"Toggle aside menu\"/>"
msgstr "<i class=\"fa fa-lg fa-bars\" title=\"Bật/tắt menu bên cạnh\"/>"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Article not Published"
msgstr "Bài viết chưa đăng"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "Article not found"
msgstr "Không tìm thấy bài viết"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Article shared to web"
msgstr "Bài viết được chia sẻ lên web"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__can_publish
msgid "Can Publish"
msgstr "Có thể đăng"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__is_published
#: model_terms:ir.ui.view,arch_db:website_knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:website_knowledge.knowledge_article_view_tree
msgid "Is Published"
msgstr "Được đăng"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "It and its published children can be read by anyone"
msgstr "Bất cứ ai cũng có thể đọc nó và phần phụ được đăng của nó"

#. module: website_knowledge
#: model:ir.model,name:website_knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr "Bài viết kiến thức"

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.articles_template
msgid "Load more"
msgstr "Tải thêm"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/editor/embedded_components/view/view_placeholder.xml:0
msgid "Log in"
msgstr "Đăng nhập"

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.public_sidebar
msgid "No article found"
msgstr "Không tìm thấy bài viết nào"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Only specific people can access"
msgstr "Chỉ những người nhất định có thể truy cập"

#. module: website_knowledge
#: model:ir.actions.server,name:website_knowledge.knowledge_action_publish_articles
msgid "Publish Articles"
msgstr "Đăng bài viết"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Publish this Article and its children on the web"
msgstr "Đăng bài viết này và các bài viết phụ của nó trên web"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Search an article..."
msgstr "Tìm kiếm một bài viết..."

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Share to web"
msgstr "Chia sẻ lên trang web"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "Sign in"
msgstr "Đăng nhập"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__summary
msgid "Summary"
msgstr "Tóm tắt"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid ""
"The article you are trying the read has either been removed or you do not "
"have access to it."
msgstr ""
"Bài viết bạn đang cố đọc đã bị xóa hoặc bạn không có quyền truy cập vào bài "
"viết đó."

#. module: website_knowledge
#: model:ir.model.fields,help:website_knowledge.field_knowledge_article__website_url
msgid "The full URL to access the document through the website."
msgstr "URL đầy đủ để truy cập tài liệu thông qua trang web."

#. module: website_knowledge
#. odoo-python
#: code:addons/website_knowledge/controllers/main.py:0
msgid ""
"This Article cannot be unfolded. Either you lost access to it or it has been"
" deleted."
msgstr ""
"Không thể hiển thị bài viết này. Hoặc bạn đã mất quyền truy cập bài viết "
"hoặc bài viết đã bị xóa."

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/editor/embedded_components/view/view_placeholder.xml:0
msgid "This view is only available for internal users"
msgstr "Chế độ xem này chỉ khả dụng với người dùng nội bộ"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Toggle aside menu"
msgstr "Chuyển đổi menu sang bên"

#. module: website_knowledge
#: model:ir.actions.server,name:website_knowledge.knowledge_action_unpublish_articles
msgid "Unpublish Articles"
msgstr "Huỷ đăng bài viết"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "Untitled"
msgstr "Không có tiêu đề"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__website_published
msgid "Visible on current website"
msgstr "Hiển thị trên trang web hiện tại"

#. module: website_knowledge
#: model:ir.model,name:website_knowledge.model_website
msgid "Website"
msgstr "Trang web"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__website_url
msgid "Website URL"
msgstr "URL trang web"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "loader"
msgstr "bộ tải"
