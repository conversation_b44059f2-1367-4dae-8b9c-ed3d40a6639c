# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_knowledge
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:16+0000\n"
"PO-Revision-Date: 2024-10-25 09:16+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "<i class=\"fa fa-lg fa-bars\" title=\"Toggle aside menu\"/>"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Article not Published"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "Article not found"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Article shared to web"
msgstr ""

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__can_publish
msgid "Can Publish"
msgstr ""

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__is_published
#: model_terms:ir.ui.view,arch_db:website_knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:website_knowledge.knowledge_article_view_tree
msgid "Is Published"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "It and its published children can be read by anyone"
msgstr ""

#. module: website_knowledge
#: model:ir.model,name:website_knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr ""

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.articles_template
msgid "Load more"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/editor/embedded_components/view/view_placeholder.xml:0
msgid "Log in"
msgstr ""

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.public_sidebar
msgid "No article found"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Only specific people can access"
msgstr ""

#. module: website_knowledge
#: model:ir.actions.server,name:website_knowledge.knowledge_action_publish_articles
msgid "Publish Articles"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Publish this Article and its children on the web"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Search an article..."
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Share to web"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "Sign in"
msgstr ""

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__summary
msgid "Summary"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid ""
"The article you are trying the read has either been removed or you do not "
"have access to it."
msgstr ""

#. module: website_knowledge
#: model:ir.model.fields,help:website_knowledge.field_knowledge_article__website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website_knowledge
#. odoo-python
#: code:addons/website_knowledge/controllers/main.py:0
msgid ""
"This Article cannot be unfolded. Either you lost access to it or it has been"
" deleted."
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/editor/embedded_components/view/view_placeholder.xml:0
msgid "This view is only available for internal users"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Toggle aside menu"
msgstr ""

#. module: website_knowledge
#: model:ir.actions.server,name:website_knowledge.knowledge_action_unpublish_articles
msgid "Unpublish Articles"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "Untitled"
msgstr ""

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_knowledge
#: model:ir.model,name:website_knowledge.model_website
msgid "Website"
msgstr ""

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__website_url
msgid "Website URL"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "loader"
msgstr ""
