<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_references_grid" inherit_id="website.s_references_grid">
    <!-- Section -->
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_cc2" remove="o_cc1" separator=" "/>
    </xpath>
    <!-- Grid gap -->
    <xpath expr="//div[hasclass('o_grid_mode')]" position="attributes">
        <attribute name="style" add="gap: 8px;" separator=";"/>
    </xpath>
    <!-- Title -->
    <xpath expr="//h2" position="replace" mode="inner">
        Our <strong>References</strong>
    </xpath>
    <!-- Column #01 -->
    <xpath expr="//div[hasclass('col-lg-2')][1]" position="attributes">
        <attribute name="class" add="o_cc o_cc1" separator=" "/>
        <attribute name="style" add="box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 2px 0px !important;" separator=";"/>
    </xpath>
    <!-- Column #02 -->
    <xpath expr="//div[hasclass('col-lg-2')][2]" position="attributes">
        <attribute name="class" add="o_cc o_cc1" separator=" "/>
        <attribute name="style" add="box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 2px 0px !important;" separator=";"/>
    </xpath>
    <!-- Column #03 -->
    <xpath expr="//div[hasclass('col-lg-2')][3]" position="attributes">
        <attribute name="class" add="o_cc o_cc1" separator=" "/>
        <attribute name="style" add="box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 2px 0px !important;" separator=";"/>
    </xpath>
    <!-- Column #04 -->
    <xpath expr="//div[hasclass('col-lg-2')][4]" position="attributes">
        <attribute name="class" add="o_cc o_cc1" separator=" "/>
        <attribute name="style" add="box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 2px 0px !important;" separator=";"/>
    </xpath>
    <!-- Column #05 -->
    <xpath expr="//div[hasclass('col-lg-2')][5]" position="attributes">
        <attribute name="class" add="o_cc o_cc1" separator=" "/>
        <attribute name="style" add="box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 2px 0px !important;" separator=";"/>
    </xpath>
    <!-- Column #06 -->
    <xpath expr="//div[hasclass('col-lg-2')][6]" position="attributes">
        <attribute name="class" add="o_cc o_cc1" separator=" "/>
        <attribute name="style" add="box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 2px 0px !important;" separator=";"/>
    </xpath>
</template>

</odoo>
