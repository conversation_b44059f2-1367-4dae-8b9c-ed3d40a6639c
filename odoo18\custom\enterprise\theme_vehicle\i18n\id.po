# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_vehicle
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:31+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quotes_carousel_minimal
msgid ""
"\" A premier choice for luxury cars. <br/>Expert maintenance, exquisite "
"service, and utmost professionalism. \""
msgstr ""
"\" A premier choice for luxury cars. <br/>Expert maintenance, exquisite "
"service, and utmost professionalism. \""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quotes_carousel_minimal
msgid ""
"\" Outstanding service and craftsmanship! <br/>They ensure every luxury "
"vehicle is in perfect condition. \""
msgstr ""
"\" Outstanding service and craftsmanship! <br/>They ensure every luxury "
"vehicle is in perfect condition. \""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quotes_carousel_minimal
msgid ""
"\" Their service is exceptional. <br/>Premium care, top-notch quality, and "
"unparalleled attention to detail. \""
msgstr ""
"\" Their service is exceptional. <br/>Premium care, top-notch quality, and "
"unparalleled attention to detail. \""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid "$ 2.4B"
msgstr "$ 2.4M"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$1,200.00"
msgstr "$1,200.00"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$2,500.00"
msgstr "$2,500.00"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$3,000.00"
msgstr "$3,000.00"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$4,000.00"
msgstr "$4,000.00"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$450.00"
msgstr "$450.00"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "$800.00"
msgstr "$800.00"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid "+300,000"
msgstr "+300,000"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid ""
"75% of our clients choose to upgrade to the latest models every 3 years, "
"showcasing strong brand loyalty and trust."
msgstr ""
"75% klien kami memilih untuk mengupgrade ke model terkini setiap 3 tahun, "
"menunjukkan loyalitas brand dan kepercayaan yang kuat."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_call_to_action
msgid "<b>50,000 pre-orders</b> already registered."
msgstr "<b>50,000 pre-orders</b> already registered."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_text
msgid "<b>A greener lifestyle</b>"
msgstr "<b>A greener lifestyle</b>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid "<b>The world is yours</b>"
msgstr "<b>The world is yours</b>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid ""
"<br/>Offering a wide selection of quality cars to suit every need and "
"budget.<br/><br/>"
msgstr ""
"<br/>Menawarkan banyak pilihan mobil berkualitas yang cocok untuk setiap "
"kebutuhan dan anggaran.<br/><br/>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(29, 32, 48) 0%, rgb(222, 222, 222) 49%);\">\n"
"            87%\n"
"        </font>"
msgstr ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(29, 32, 48) 0%, rgb(222, 222, 222) 49%);\">\n"
"            87%\n"
"        </font>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Top-"
"rated vehicles"
msgstr ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Top-"
"rated vehicles"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.configurator_s_three_columns
msgid "<span class=\"d-inline-block mb-4\"><b>From $14,000</b></span>"
msgstr "<span class=\"d-inline-block mb-4\"><b>From $14,000</b></span>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.configurator_s_three_columns
msgid "<span class=\"d-inline-block mb-4\"><b>From $19,000</b></span>"
msgstr "<span class=\"d-inline-block mb-4\"><b>From $19,000</b></span>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.configurator_s_three_columns
msgid "<span class=\"d-inline-block mb-4\"><b>From $25,000</b></span>"
msgstr "<span class=\"d-inline-block mb-4\"><b>From $25,000</b></span>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_picture
msgid ""
"<span class=\"text-o-color-3\">Our uniquely designed LED headlights are not "
"only gorgeous but powerfully light your way.</span>"
msgstr ""
"<span class=\"text-o-color-3\">Our uniquely designed LED headlights are not "
"only gorgeous but powerfully light your way.</span>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_title
msgid "A Deep Dive into Luxury and Innovation"
msgstr "A Deep Dive into Luxury and Innovation"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid ""
"A bold statement in every detail, the Koran X captures the spirit of modern "
"adventure. Built for those who refuse to compromise on performance and "
"style."
msgstr ""
"A bold statement in every detail, the Koran X captures the spirit of modern "
"adventure. Built for those who refuse to compromise on performance and "
"style."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid ""
"Achieve optimal performance with a sleek, aerodynamic design that reduces "
"drag and enhances stability, ensuring superior speed and efficiency."
msgstr ""
"Achieve optimal performance with a sleek, aerodynamic design that reduces "
"drag and enhances stability, ensuring superior speed and efficiency."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "Aerodynamic Design"
msgstr "Desain Aerodinamis"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_title
msgid "All our models"
msgstr "Semua model-model kami"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid ""
"Benefit from competitive financing plans and leasing options designed to "
"make your car purchase as affordable as possible."
msgstr ""
"Benefit from competitive financing plans and leasing options designed to "
"make your car purchase as affordable as possible."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Bespoke Automotive Solutions"
msgstr "Bespoke Automotive Solutions"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_freegrid
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quadrant
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_striped_center_top
msgid "Book a Service"
msgstr "Book Layanan"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid "Book a test drive"
msgstr "Book test drive"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #1"
msgstr "Brakes #1"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #2"
msgstr "Brakes #2"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #3"
msgstr "Brakes #3"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #4"
msgstr "Brakes #4"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #5"
msgstr "Brakes #5"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #6"
msgstr "Brakes #6"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_picture
msgid ""
"Bring the KORAN to life on your smartphone or tablet so you can visualise it"
" for yourself"
msgstr ""
"Bring the KORAN to life on your smartphone or tablet so you can visualise it"
" for yourself"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid "Browse our inventory   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr "Browse inventory kami   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid ""
"Clients saved $2.4 billion by choosing our eco-efficient and cost-effective "
"vehicles."
msgstr ""
"Klien menghemat $2.4 milyar dengan memilih kendaraan kami yang lebih eco-"
"efficient dan hemat-biaya."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Complete maintenance service covering engine check, transmission fluid "
"change, brake inspection, and system diagnostics for optimal performance."
msgstr ""
"Complete maintenance service covering engine check, transmission fluid "
"change, brake inspection, and system diagnostics for optimal performance."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid "Comprehensive Support"
msgstr "Bantuan Komprehensif"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Comprehensive detailing service including exterior polishing, interior "
"cleaning, and protective coatings for a showroom finish."
msgstr ""
"Comprehensive detailing service including exterior polishing, interior "
"cleaning, and protective coatings for a showroom finish."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.configurator_s_three_columns
msgid "Configure"
msgstr "Konfigurasi"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cta_box
msgid "Contact us"
msgstr "Hubungi kami"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Custom Interior Upgrades"
msgstr "Upgrade Interior Kustom"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid "Custom Vehicle Options"
msgstr "Opsi-Opsi Kendaraan Kustom"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid ""
"Designed for the thrill-seeker, the Koran GT brings race-inspired "
"performance to the streets. Precision-tuned for speed, it promises an "
"driving experience."
msgstr ""
"Designed for the thrill-seeker, the Koran GT brings race-inspired "
"performance to the streets. Precision-tuned for speed, it promises an "
"driving experience."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Discover Excellence in Luxury Vehicle Care"
msgstr "Discover Excellence in Luxury Vehicle Care"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Discover our exclusive range of services for luxury vehicles, designed to "
"provide unparalleled quality and sophistication. Experience excellence on "
"every drive."
msgstr ""
"Discover our exclusive range of services for luxury vehicles, designed to "
"provide unparalleled quality and sophistication. Experience excellence on "
"every drive."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid ""
"Discover unique and advanced features that set our vehicles apart, providing"
" you with exceptional performance and cutting-edge technology."
msgstr ""
"Discover unique and advanced features that set our vehicles apart, providing"
" you with exceptional performance and cutting-edge technology."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid ""
"Discover unparalleled automotive care with our expert services that blend "
"precision, luxury, and exceptional performance."
msgstr ""
"Discover unparalleled automotive care with our expert services that blend "
"precision, luxury, and exceptional performance."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_framed_intro
msgid "Drive Your Dream: Quality Cars for Every Journey"
msgstr "Drive Your Dream: Quality Cars for Every Journey"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_striped_center_top
msgid "Drive with Confidence and Style"
msgstr "Drive with Confidence and Style"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_card_offset
msgid "Drive with confidence."
msgstr "Drive with confidence."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_unveil
msgid "Drive your Passion"
msgstr "Drive your Passion"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid "Driving excellence with luxury vehicles"
msgstr "Driving excellence with luxury vehicles"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features
msgid "Dual Motor"
msgstr "Dual Motor"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_text
msgid "Electric Driving"
msgstr "Electric Driving"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_masonry_block_image_texts_image_template
msgid "Electrifying <b>Performance</b>."
msgstr "Electrifying <b>Performance</b>."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid ""
"Elevate your driving experience with our bespoke maintenance and repair "
"solutions."
msgstr ""
"Elevate your driving experience with our bespoke maintenance and repair "
"solutions."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid ""
"Enjoy exceptional control and responsiveness with advanced suspension "
"systems and precise steering, offering unmatched agility and cornering "
"capabilities."
msgstr ""
"Enjoy exceptional control and responsiveness with advanced suspension "
"systems and precise steering, offering unmatched agility and cornering "
"capabilities."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Excellence and Craftsmanship"
msgstr "Excellence and Craftsmanship"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Exclusive Exterior Customization"
msgstr "Kustomisasi Eksterior Eksklusif"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid "Exclusive Features"
msgstr "Fitur-Fitur Eksklusif"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid ""
"Experience thrilling acceleration and top speeds with our sports car’s high-"
"performance engine, designed for an exhilarating driving experience on both "
"the track and the road."
msgstr ""
"Experience thrilling acceleration and top speeds with our sports car’s high-"
"performance engine, designed for an exhilarating driving experience on both "
"the track and the road."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid "Experience top-tier luxury"
msgstr "Experience top-tier luxury"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid "Expert Vehicle Service"
msgstr "Layanan Kendaraan Ahli"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Expert technicians specializing in luxury and exotic vehicles"
msgstr "Teknisi ahli yang berspesialis di kendaraan mewah dan eksotis"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid ""
"Explore more and find premium services for your luxury vehicle, crafted to "
"ensure unrivaled quality and customer satisfaction."
msgstr ""
"Temukan lebih banyak dan cari layanan premium untuk kendaraan mewah Anda, "
"dibuat untuk memastikan kualitas tak tertandingi dan kepuasan pelanggan."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_unveil
msgid ""
"Explore our exceptional range of cars designed for performance and luxury."
msgstr ""
"Jelajahi beragam macam mobil kami yang dirancang untuk performa dan "
"kemewahan."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid ""
"Explore our extensive inventory of new and pre-owned cars to find the "
"perfect vehicle that fits your needs and budget."
msgstr ""
"Temukan inventaris lengkap kami untuk mobil baru dan bekas untuk mencari "
"kendaraan yang sempurna yang cocok dengan kebutuhan dan anggaran Anda."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_images_mosaic
msgid "Explore premium maintenance and bespoke care for your high-end car."
msgstr ""
"Temukan maintenance premium dan perbaikan khusus untuk mobil kelas atas "
"Anda."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid "Find your perfect ride<br/>with premium car sales"
msgstr "Temukan kendaraan sempurna Anda<br/>dengan sales mobil premium"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid "Flexible Financing Options"
msgstr "Opsi Keuangan Fleksibel"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Full-Service Maintenance"
msgstr "Maintenance Full-Service"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "High-Performance Engine"
msgstr "Engine High-Performance"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "High-Performance Tuning"
msgstr "Tuning High-Performance"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid ""
"Immerse yourself in a driver-focused cockpit with sport seats, intuitive "
"controls, and premium materials, all crafted to enhance your driving "
"pleasure and comfort."
msgstr ""
"Immerse yourself in a driver-focused cockpit with sport seats, intuitive "
"controls, and premium materials, all crafted to enhance your driving "
"pleasure and comfort."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Innovation and Performance"
msgstr "Inovasi dan Performa"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_call_to_action
msgid "Join the early joiners or request for a trial on track."
msgstr "Join the early joiners or request for a trial on track."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid ""
"Keep your car in top condition with our comprehensive service and "
"maintenance options, handled by skilled technicians."
msgstr ""
"Pastikan mobil Anda selalu dalam kondisi prima dengan layanan komprehensif "
"kami dan opsi-opsi maintenance, ditangani oleh teknisi andal."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_card_offset
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_freegrid
msgid ""
"Keep your vehicle in top condition with our comprehensive repair and "
"maintenance services. From cars to motorbikes, we offer expert solutions to "
"ensure safe and smooth rides."
msgstr ""
"Pastikan mobil Anda dalam kondisi prima dengan perbaikan komprehensif dan "
"layanan maintenance kami. Dari mobil sampai sepeda motor, kami menawarkan "
"solusi ahli untuk memastikan perjalanan yang aman dan mulus."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quadrant
msgid ""
"Keep your vehicle in top condition with our expert repair and maintenance "
"services. From cars to motorbikes, we’ve got you covered.<br/><br/> Drive "
"with confidence and style."
msgstr ""
"Pastikan mobil Anda dalam kondisi prima dengan perbaikan ahli dan layanan "
"maintenance kami. Dari mobil sampai sepeda motor, Anda tetap "
"terjaga.<br/><br/> Drive with confidence and style."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_striped_center_top
msgid ""
"Keep your vehicles running smoothly with our expert repair and maintenance "
"services, designed for performance and reliability."
msgstr ""
"Pastikan kendaraan Anda berjalan dengan mulus dengan layanan perbaikan dan "
"maintenance ahli kami, dirancang untuk performa dan keandalan."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid "Key Metrics of our Achievements"
msgstr "Metrik-Metrik Kunci Pencapaian kami"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Koran GT"
msgstr "Koran GT"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Koran Mini"
msgstr "Koran Mini"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Koran X"
msgstr "Koran X"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features
msgid "Long Range"
msgstr "Long Range"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cta_box
msgid ""
"Looking for a leasing plan or a brand new car ? We got you covered<br/><br/>"
msgstr ""
"Mencari rencana cicilan atau mobil baru ? Kami bisa membantu Anda<br/><br/>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Luxury Vehicle Servicing"
msgstr "Servis Kendaraan Mewah"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_media_list
msgid "News"
msgstr "Berita"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid "No compromise"
msgstr "No compromise"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_images_mosaic
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Our Luxury Vehicle Services"
msgstr "Layanan Kendaraan Mewah Kami"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_references
msgid "Our Partners"
msgstr "Mitra Kami"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_empowerment
msgid "Our cars"
msgstr "Mobil-mobil kami"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid ""
"Our commitment to innovation, performance, and sustainability drives us "
"forward, enabling us to deliver excellence across all categories of the "
"automotive industry."
msgstr ""
"Our commitment to innovation, performance, and sustainability drives us "
"forward, enabling us to deliver excellence across all categories of the "
"automotive industry."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid ""
"Our team provides full support throughout your vehicle purchase and "
"ownership experience, from initial consultation to ongoing service."
msgstr ""
"Our team provides full support throughout your vehicle purchase and "
"ownership experience, from initial consultation to ongoing service."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_grid
msgid "Our web store"
msgstr "Our web store"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid ""
"Performance and design were the key words during the conception of this "
"super car. No compromises were made to keep the pleasure of driving despite "
"those constraints."
msgstr ""
"Performance and design were the key words during the conception of this "
"super car. No compromises were made to keep the pleasure of driving despite "
"those constraints."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Performance tuning service including engine optimization, suspension "
"upgrades, and exhaust modifications to enhance driving dynamics and power."
msgstr ""
"Performance tuning service including engine optimization, suspension "
"upgrades, and exhaust modifications to enhance driving dynamics and power."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Personalized exterior modifications such as custom paint jobs, aerodynamic "
"enhancements, and bespoke alloy wheels to reflect your style."
msgstr ""
"Personalized exterior modifications such as custom paint jobs, aerodynamic "
"enhancements, and bespoke alloy wheels to reflect your style."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Personalized service to ensure your vehicle's optimal performance"
msgstr "Personalized service to ensure your vehicle's optimal performance"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "Precision Handling"
msgstr "Precision Handling"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_striped_center_top
msgid "Precision in every detail"
msgstr "Precision in every detail"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "Premium Detailing Package"
msgstr "Premium Detailing Package"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Premium Services for Your High-End Automotive Needs"
msgstr "Layanan Premium untuk Kebutuhan Otomatis Kelas Atas Anda"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_carousel_intro
msgid "Premium automotive services"
msgstr "Layanan otomatif premium"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_card_offset
msgid "Reliable Vehicle Services &amp; Repairs"
msgstr "Layanan &amp; Perbaikan Kendaraan Terpercaya"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_freegrid
msgid "Reliable Vehicle Services and Maintenance"
msgstr "Layanan dan Maintenance Kendaraan Terpercaya"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_masonry_block_image_texts_image_template
msgid "Smarter <b>Range</b>."
msgstr "Smarter <b>Range</b>."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Specialized servicing tailored for high-end vehicles, including advanced "
"diagnostics, custom parts, and manufacturer-recommended procedures."
msgstr ""
"Layanan spesialis khusus untuk kendaraan kelas atas, termasuk diagnostik "
"tingkat lanjut, custom part, dan prosedur yang direkomendasikan pihak "
"manufaktur."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "Sporty Interior"
msgstr "Interior Sporty"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cover
msgid "Start the engine"
msgstr "Start the engine"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "State-of-the-art facilities for superior maintenance"
msgstr "Fasilitas termutakhir untuk maintenance terbaik"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Sustainable Luxury"
msgstr "Kemewahan Berkelanjutan"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid ""
"Tailored interior enhancements including premium leather upholstery, bespoke"
" trims, and advanced infotainment systems for a unique driving experience."
msgstr ""
"Tailored interior enhancements including premium leather upholstery, bespoke"
" trims, and advanced infotainment systems for a unique driving experience."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid "Take advantage of a free track trial to discover it."
msgstr "Take advantage of a free track trial to discover it."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cover
msgid "Take it all. Compliments too. <br/><br/>"
msgstr "Ambil semuanya. Termasuk barang komplementer juga. <br/><br/>"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cover
msgid "The New KORAN X"
msgstr "The New KORAN X"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cta_box
msgid "The best car dealer<br/>in your local area"
msgstr "Dealer mobil terbaik<br/>di daerah lokal Anda"

#. module: theme_vehicle
#: model:ir.model,name:theme_vehicle.model_theme_utils
msgid "Theme Utils"
msgstr "Utilities Tema"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features
msgid "Top Speed"
msgstr "Top Speed"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_title
msgid ""
"Transform your driving experience with our exclusive collection, where "
"opulence meets cutting-edge technology. Elevate your journey with vehicles "
"that blend sophistication and performance seamlessly."
msgstr ""
"Transform your driving experience with our exclusive collection, where "
"opulence meets cutting-edge technology. Elevate your journey with vehicles "
"that blend sophistication and performance seamlessly."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_images
msgid "Trust us to keep your luxury vehicle in pristine condition"
msgstr "Trust us to keep your luxury vehicle in pristine condition"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cards_grid
msgid "Unleash the Power of Our Sports Cars"
msgstr "Unleash the Power of Our Sports Cars"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid ""
"Unleashing power with elegance, the Koran Mini blends advanced engineering "
"with classic design, delivering a thrilling ride that’s both swift and "
"smooth."
msgstr ""
"Unleashing power with elegance, the Koran Mini blends advanced engineering "
"with classic design, delivering a thrilling ride that’s both swift and "
"smooth."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_quadrant
msgid "Vehicle Services"
msgstr "Layanan Kendaraan"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_picture
msgid "View the all-new KORAN in 3D wherever you are"
msgstr "View the all-new KORAN in 3D wherever you are"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_key_benefits
msgid ""
"We offer personalized vehicle configurations to match your preferences and "
"needs, ensuring you get the perfect fit for your lifestyle."
msgstr ""
"Kami menawarkan konfigurasi kendaraan terpersonalisasi agar cocok dengan "
"preferensi dan kebutuhan Anda, memastikan Anda mendapatkan barang yang cocok"
" sempurna dengan lifestyle Anda."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid ""
"We offer the latest advancements in automotive technology and design. "
"Leveraging cutting-edge features, we ensure every vehicle provides "
"exceptional performance and sophistication."
msgstr ""
"We offer the latest advancements in automotive technology and design. "
"Leveraging cutting-edge features, we ensure every vehicle provides "
"exceptional performance and sophistication."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_numbers_charts
msgid "We proudly serve over 300,000 satisfied customers."
msgstr "Kami dengan bangga melayani lebih dari 300,000 pelanggan yang puas."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid ""
"We provide tailored luxury vehicles designed to meet your specific "
"preferences."
msgstr ""
"Kami menyediakan kendaraan mewah khusus yang dirancang untuk memenuhi "
"preferensi spesifik Anda."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "What we offer to our clients"
msgstr "What we offer to our clients"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_wall
msgid "Wide Vehicle Selection"
msgstr "Pemilihan Lebar Kendaraan"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid ""
"With extensive experience and superior craftsmanship, we provide high-end "
"vehicles that deliver unmatched performance and style, ensuring a luxurious "
"driving experience."
msgstr ""
"Dengan pengalaman ekstensif dan keahlian yang tinggi, kami menyediakan "
"kendaraan kelas atas yang menjanjikan performa serta style yang tidak "
"tertandingi, memastikan pengalaman mengemudi yang luar biasa."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_grid
msgid "Your advantages"
msgstr "Your advantages"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_wavy_grid
msgid "Your satisfaction and environmental impact are our priorities."
msgstr "Kepuasan Anda dan dampak lingkungan adalah prioritas kami."

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_big_number
msgid "customer satisfaction"
msgstr "kepuasan pelanggan"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "✽  Customization &amp; Upgrades"
msgstr "✽  Kustomisasi & Upgrade"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_pricelist_boxed
msgid "✽  Maintenance &amp; Care"
msgstr "✽  Maintenance &amp; Care"
