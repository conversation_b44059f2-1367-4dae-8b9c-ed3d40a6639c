<?xml version="1.0" encoding="UTF-8"?>
<odoo noupdate="1">

        <record id="timer_user_rule_create-write-delete" model="ir.rule">
            <field name="name">timer.user.rule.create-write-delete</field>
            <field name="model_id" ref="timer.model_timer_timer"/>
            <field name="domain_force">[
                ('user_id', '=', user.id)
            ]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_read" eval="1"/>
        </record>

        <record id="timer_not_user_rule_create-write-delete" model="ir.rule">
            <field name="name">timer.not.user.rule.create-write-delete</field>
            <field name="model_id" ref="timer.model_timer_timer"/>
            <field name="domain_force">[
                ('user_id', '!=', user.id)
            ]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="perm_read" eval="1"/>
        </record>

</odoo>
