# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_zap
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON>l<PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> Ažna <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:28+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON>ar<PERSON>nas Ažna <<EMAIL>>, 2024\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_key_benefits
msgid "24/7 Technical Support"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_big_number
msgid "90%"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_numbers
msgid "98%"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_banner
msgid "<b>Software innovation</b><br/> at its best."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_empowerment
msgid "<br/>Transforming ideas into impactful digital experiences.<br/><br/>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_masonry_block_default_template
msgid ""
"<em class=\"lead\">Maintain a position of constant change<br/> and "
"evolution, while always aiming<br/> for your success.</em>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_numbers
msgid "<font class=\"text-o-color-1\">Happy customers</font>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_numbers
msgid "<font class=\"text-o-color-1\">Languages</font>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_numbers
msgid "<font class=\"text-o-color-1\">Solutions</font>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_cta_card
msgid ""
"<i class=\"fa fa-check text-o-color-1\" role=\"presentation\" "
"contenteditable=\"false\"/> Attractive Salary"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_cta_card
msgid ""
"<i class=\"fa fa-check text-o-color-1\" role=\"presentation\" "
"contenteditable=\"false\"/> Continuous Learning"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_cta_card
msgid ""
"<i class=\"fa fa-check text-o-color-1\" role=\"presentation\" "
"contenteditable=\"false\"/> Healthy Environement"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_discovery
msgid ""
"<i class=\"fa fa-circle text-success\" role=\"presentation\"/>  We're hiring"
" now!"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" "
"role=\"img\"/>  Innovative solutions"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_key_benefits
msgid ""
"Access the latest advancements and tools that enhance your digital "
"experience and keep you ahead in the ever-evolving tech landscape."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_showcase
msgid "Agile Approach"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_features
msgid "All our data centers <br/>are emission-free."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_features
msgid "All types of files <br/>are allowed."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_three_columns
msgid "Benefits"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_showcase
msgid "Book an appointment"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_quadrant
msgid ""
"Boost your brand’s online presence with our expert digital marketing "
"services. From copywriting to media campaigns, we tailor strategies that "
"drive results.<br/><br/> Elevate your brand with us."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_striped_center_top
msgid ""
"Boost your brand’s presence with our expert digital marketing services, "
"tailored to drive engagement and results."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_card_offset
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_freegrid
msgid ""
"Boost your brand’s visibility and engagement with our expert digital "
"marketing services. From copywriting to media strategies, we tailor "
"solutions to meet your unique needs."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_empowerment
msgid "Building the future<br/>with NovaDev Solutions"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "Business Strategy Consulting"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_comparisons
msgid ""
"Check out our flexible pricing plans with no hidden fees. Contact us for a "
"custom quote."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_color_blocks_2
msgid "Cloud <b>Solution</b>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "Cloud Computing Solutions"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_showcase
msgid "Coding Excellence"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid ""
"Comprehensive financial planning, including risk assessment, investment "
"strategy, and tax optimization for your business."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_discovery
msgid "Contact Us"
msgstr "Susisiekite su mumis"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_cta_box
msgid "Contact us"
msgstr "Susisiekite su mumis"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_cta_box
msgid "Contact us and get started.<br/><br/>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_cta_box
msgid "Curious to know more?"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_key_benefits
msgid "Customized Digital Solutions"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid ""
"Customized HR solutions, including talent acquisition, employee training, "
"and performance management to boost productivity."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_key_images
msgid "Customized solutions for your business needs"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_key_benefits
msgid "Cutting-Edge Technology"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_quadrant
msgid "Digital Marketing"
msgstr "Skaitmeninė rinkodara"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "Digital Marketing Services"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_key_images
msgid "Driving Success Through Innovation"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_features
msgid "Eco-Friendly"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_card_offset
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_freegrid
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_striped_center_top
msgid "Elevate Your Brand with Digital Marketing"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_unveil
msgid "Empower your digital journey"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_key_images
msgid "Empowering Businesses to Reach New Heights"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid ""
"Enhance your business operations with our comprehensive suite of "
"professional services, tailored to meet your corporate needs."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_discovery
msgid "Experience the real<br/>innovation"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_discovery
msgid ""
"Experience top-tier software solutions designed to streamline "
"operations,<br/> boost efficiency, and drive innovation for your business."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid ""
"Expert advice on business planning, market analysis, and competitive "
"strategy to help your company achieve its goals."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_key_images
msgid "Expert advice to navigate complex challenges"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_three_columns
msgid "Features"
msgstr "Funkcijos"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "Financial Advisory Services"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_discovery
msgid "Get a Quote"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_banner
msgid ""
"Harness the power of disruptive technologies to increase<br/> your day-to-"
"day business operations."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "Human Resources Management"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "IT Support Services"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_color_blocks_2
msgid ""
"Keep your files in one place. <br/>Manage documents online with an easy to "
"use interface. <br/>Rely on an highly secure cloud storage."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_carousel_intro
msgid "Leading the future with innovation"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_card_offset
msgid "Let’s grow your brand together."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_striped_center_top
msgid "Marketing crafted for impact"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_showcase
msgid "Meet Our Specialists"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_references_grid
msgid "Our <strong>References</strong>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "Our Corporate Services"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_references
msgid "Our References"
msgstr "Mūsų rekomendacijos"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_banner
msgid "Our Solutions"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_empowerment
msgid "Our services"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_showcase
msgid ""
"Our specialists bring a wealth of knowledge and experience to deliver top-"
"quality solutions every time."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_key_benefits
msgid ""
"Our team offers round-the-clock support to ensure your digital platforms run"
" smoothly and any issues are swiftly addressed."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_key_images
msgid "Partner with us to unlock your potential"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_key_images
msgid "Proven strategies for sustainable growth"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid ""
"Reliable IT support, network management, and cybersecurity solutions to "
"safeguard your business operations and data integrity."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid ""
"Scalable cloud services to enhance your business’s flexibility and data "
"accessibility, supporting remote work and collaboration."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_features
msgid "Security"
msgstr "Saugumas"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_showcase
msgid "Security First"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_discovery
msgid "See more  <i class=\"fa fa-long-arrow-right\" role=\"presentation\"/>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_empowerment
msgid "See our projects   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_three_columns
msgid "Services"
msgstr "Paslaugos"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_quadrant
msgid "Start Campaign"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_freegrid
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_striped_center_top
msgid "Start Your Campaign"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_empowerment
msgid "Start a project"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "Starting at $1,500"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "Starting at $1,800"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "Starting at $2,000"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "Starting at $2,500"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "Starting at $3,000"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid ""
"Strategic online marketing, SEO optimization, and social media management to"
" boost your brand visibility and customer engagement."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_masonry_block_default_template
msgid "Super <b>Easy</b>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_masonry_block_default_template
msgid "Super <b>Fast</b>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_features
msgid "Support"
msgstr "Palaikymas"

#. module: theme_zap
#: model:ir.model,name:theme_zap.model_theme_utils
msgid "Theme Utils"
msgstr "Tema Utils"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_unveil
msgid ""
"Unlock limitless possibilities with our innovative cloud solutions and agile"
" technology."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_showcase
msgid ""
"Using agile methods, we deliver flexible, iterative solutions that adapt "
"quickly to changing business needs and feedback."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_striped
msgid "We deliver powerful solutions"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_showcase
msgid ""
"We focus on writing clean, efficient code, ensuring scalable and high-"
"performance software that meets industry standards."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_showcase
msgid ""
"We integrate strong security measures at every stage, protecting your "
"software and data from vulnerabilities and threats."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_features
msgid "We keep you safe <br/>in the cloud."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_striped
msgid ""
"We provide innovative, tailored strategies that drive results and enhance "
"business performance effectively."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_key_benefits
msgid ""
"We provide tailored digital strategies and solutions that align with your "
"specific goals, driving innovation and efficiency in your online presence."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_references
msgid ""
"We're excited to have the opportunity to collaborate with these outstanding "
"companies!"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_cta_card
msgid "We’re seeking exceptional talent to join our team"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "✽  Consulting Services"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_pricelist_boxed
msgid "✽  Technology Solutions"
msgstr ""
