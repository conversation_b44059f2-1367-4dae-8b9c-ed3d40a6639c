<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_company_team_basic" inherit_id="website.s_company_team_basic">
    <!-- Person 1 -->
    <xpath expr="(//img)[1]" position="attributes">
        <attribute name="class" remove="rounded-circle" separator=" "/>
    </xpath>
    <!-- Person 2 -->
    <xpath expr="(//img)[2]" position="attributes">
        <attribute name="class" remove="rounded-circle" separator=" "/>
    </xpath>
    <!-- Person 3 -->
    <xpath expr="(//img)[3]" position="attributes">
        <attribute name="class" remove="rounded-circle" separator=" "/>
    </xpath>
    <!-- Person 4 -->
    <xpath expr="(//img)[4]" position="attributes">
        <attribute name="class" remove="rounded-circle" separator=" "/>
    </xpath>
</template>

</odoo>
